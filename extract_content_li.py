import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
from urllib.parse import urljoin

class ContentLiExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(self.headers)
        self.base_url = 'https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html'
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            print(f"页面访问成功，状态码: {response.status_code}")
            print(f"页面大小: {len(response.content)} 字节")
            return response
        except Exception as e:
            print(f"页面访问失败: {e}")
            return None
    
    def find_content_containers(self, soup):
        """查找所有可能的content容器"""
        print("\n=== 查找content容器 ===")
        
        # 查找不同的content类选择器
        selectors = [
            '.content',           # class="content"
            '[class*="content"]', # class包含content
            '#content',           # id="content"
            '.main-content',      # class="main-content"
            '.page-content',      # class="page-content"
            '.search-content'     # class="search-content"
        ]
        
        found_containers = []
        
        for selector in selectors:
            containers = soup.select(selector)
            if containers:
                print(f"选择器 '{selector}': 找到 {len(containers)} 个容器")
                for i, container in enumerate(containers):
                    li_elements = container.find_all('li')
                    if li_elements:
                        print(f"  容器 {i+1}: 包含 {len(li_elements)} 个 <li> 元素")
                        found_containers.append({
                            'selector': selector,
                            'container_index': i,
                            'container': container,
                            'li_count': len(li_elements)
                        })
        
        return found_containers
    
    def extract_li_data(self, li_element, index):
        """从单个li元素中提取详细数据"""
        data = {
            'li_index': index + 1,
            'text_content': '',
            'links': [],
            'images': [],
            'classes': '',
            'data_attributes': {},
            'inner_html': '',
            'child_elements': []
        }

        try:
            # 提取完整文本内容
            data['text_content'] = li_element.get_text(strip=True)

            # 提取class属性
            classes = li_element.get('class', [])
            data['classes'] = ' '.join(classes) if classes else ''

            # 提取所有属性（包括data属性）
            for attr, value in li_element.attrs.items():
                if attr.startswith('data-'):
                    data['data_attributes'][attr] = value

            # 详细提取所有链接
            links = li_element.find_all('a')
            for link in links:
                href = link.get('href', '')
                if href:
                    if href.startswith('/'):
                        href = urljoin(self.base_url, href)

                    link_data = {
                        'url': href,
                        'text': link.get_text(strip=True),
                        'title': link.get('title', ''),
                        'target': link.get('target', ''),
                        'class': ' '.join(link.get('class', [])),
                        'id': link.get('id', ''),
                        'onclick': link.get('onclick', ''),
                        'all_attributes': dict(link.attrs)
                    }
                    data['links'].append(link_data)

            # 详细提取所有图片
            images = li_element.find_all('img')
            for img in images:
                src = img.get('src', '')
                if src and src.startswith('/'):
                    src = urljoin(self.base_url, src)

                img_data = {
                    'src': src,
                    'alt': img.get('alt', ''),
                    'title': img.get('title', ''),
                    'width': img.get('width', ''),
                    'height': img.get('height', ''),
                    'class': ' '.join(img.get('class', [])),
                    'id': img.get('id', ''),
                    'loading': img.get('loading', ''),
                    'data_src': img.get('data-src', ''),  # 懒加载图片
                    'all_attributes': dict(img.attrs)
                }
                data['images'].append(img_data)

            # 提取所有子元素的详细信息
            for child in li_element.find_all():
                if child.name:  # 确保是标签元素
                    child_text = child.get_text(strip=True)
                    if child_text:  # 只保存有文本内容的元素
                        child_data = {
                            'tag': child.name,
                            'text': child_text,
                            'classes': ' '.join(child.get('class', [])),
                            'id': child.get('id', ''),
                            'attributes': dict(child.attrs)
                        }
                        data['child_elements'].append(child_data)

            # 提取完整的内部HTML
            data['inner_html'] = str(li_element)

            # 尝试提取结构化的产品数据
            self.extract_structured_data(li_element, data)

            # 提取特殊元素
            self.extract_special_elements(li_element, data)

        except Exception as e:
            print(f"提取li元素数据时出错: {e}")
            data['error'] = str(e)

        return data

    def extract_special_elements(self, li_element, data):
        """提取特殊元素（按钮、表单、视频等）"""
        # 提取按钮
        buttons = li_element.find_all(['button', 'input[type="button"]', 'input[type="submit"]'])
        data['buttons'] = []
        for btn in buttons:
            data['buttons'].append({
                'tag': btn.name,
                'text': btn.get_text(strip=True),
                'type': btn.get('type', ''),
                'value': btn.get('value', ''),
                'onclick': btn.get('onclick', ''),
                'class': ' '.join(btn.get('class', []))
            })

        # 提取表单元素
        form_elements = li_element.find_all(['input', 'select', 'textarea'])
        data['form_elements'] = []
        for elem in form_elements:
            data['form_elements'].append({
                'tag': elem.name,
                'type': elem.get('type', ''),
                'name': elem.get('name', ''),
                'value': elem.get('value', ''),
                'placeholder': elem.get('placeholder', ''),
                'class': ' '.join(elem.get('class', []))
            })

        # 提取span和div中的特殊信息
        spans = li_element.find_all(['span', 'div'])
        data['special_texts'] = []
        for span in spans:
            text = span.get_text(strip=True)
            if text and len(text) < 200:  # 避免太长的文本
                classes = ' '.join(span.get('class', []))
                # 检查是否可能是价格、标题等重要信息
                if any(keyword in classes.lower() for keyword in ['price', 'title', 'name', 'brand', 'desc']):
                    data['special_texts'].append({
                        'tag': span.name,
                        'text': text,
                        'class': classes,
                        'type': self.classify_text_content(text, classes)
                    })

    def classify_text_content(self, text, classes):
        """分类文本内容类型"""
        text_lower = text.lower()
        classes_lower = classes.lower()

        # 价格相关
        if re.search(r'[¥￥$]\s*[\d,]+\.?\d*|[\d,]+\.?\d*\s*[元块]', text):
            return '价格信息'

        # 品牌相关
        brands = ['nike', 'adidas', 'puma', 'converse', 'vans', 'new balance']
        if any(brand in text_lower for brand in brands):
            return '品牌信息'

        # 尺码相关
        if re.search(r'\d{2,2}\.?\d?\s*码|\d{2,2}\.?\d?\s*号|size\s*\d+', text_lower):
            return '尺码信息'

        # 根据class判断
        if 'price' in classes_lower:
            return '价格信息'
        elif 'title' in classes_lower or 'name' in classes_lower:
            return '标题信息'
        elif 'desc' in classes_lower:
            return '描述信息'
        elif 'brand' in classes_lower:
            return '品牌信息'
        else:
            return '普通文本'
    
    def extract_structured_data(self, li_element, data):
        """尝试提取结构化的产品数据"""
        # 查找可能的产品信息
        
        # 标题
        title_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.name', '.product-name', '[class*="title"]']
        for selector in title_selectors:
            title_elem = li_element.select_one(selector)
            if title_elem:
                data['product_title'] = title_elem.get_text(strip=True)
                break
        
        # 价格
        price_selectors = ['.price', '.cost', '.money', '[class*="price"]', '[class*="cost"]']
        for selector in price_selectors:
            price_elem = li_element.select_one(selector)
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                # 提取数字价格
                price_match = re.search(r'[\d,]+\.?\d*', price_text)
                if price_match:
                    data['product_price'] = price_match.group()
                data['product_price_text'] = price_text
                break
        
        # 描述
        desc_selectors = ['.desc', '.description', '.summary', '[class*="desc"]']
        for selector in desc_selectors:
            desc_elem = li_element.select_one(selector)
            if desc_elem:
                data['product_description'] = desc_elem.get_text(strip=True)
                break
        
        # 品牌/商家
        brand_selectors = ['.brand', '.shop', '.store', '[class*="brand"]', '[class*="shop"]']
        for selector in brand_selectors:
            brand_elem = li_element.select_one(selector)
            if brand_elem:
                data['product_brand'] = brand_elem.get_text(strip=True)
                break
    
    def process_url(self, url):
        """处理指定URL"""
        # 获取页面内容
        response = self.get_page_content(url)
        if not response:
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找content容器
        containers = self.find_content_containers(soup)
        
        if not containers:
            print("未找到任何content容器")
            return []
        
        all_li_data = []
        
        # 处理每个找到的容器
        for container_info in containers:
            print(f"\n=== 处理容器: {container_info['selector']} (第{container_info['container_index']+1}个) ===")
            
            container = container_info['container']
            li_elements = container.find_all('li')
            
            print(f"找到 {len(li_elements)} 个 <li> 元素")
            
            for i, li in enumerate(li_elements):
                print(f"处理第 {i+1} 个 <li> 元素...")
                
                li_data = self.extract_li_data(li, i)
                li_data['container_selector'] = container_info['selector']
                li_data['container_index'] = container_info['container_index']
                
                all_li_data.append(li_data)
                
                # 显示提取的数据预览
                if li_data['text_content']:
                    preview = li_data['text_content'][:100] + '...' if len(li_data['text_content']) > 100 else li_data['text_content']
                    print(f"  文本内容: {preview}")
                
                if li_data['links']:
                    print(f"  包含 {len(li_data['links'])} 个链接")
                
                if li_data['images']:
                    print(f"  包含 {len(li_data['images'])} 张图片")
        
        return all_li_data
    
    def save_to_excel(self, li_data_list, filename='content_li_data.xlsx'):
        """保存数据到Excel"""
        if not li_data_list:
            print("没有数据可保存")
            return False

        try:
            # 准备详细数据表
            detailed_data = []
            links_data = []
            images_data = []
            all_elements_data = []

            for item in li_data_list:
                # 详细主数据 - 每个li的完整信息
                main_row = {
                    'Li索引': item['li_index'],
                    '容器选择器': item['container_selector'],
                    '容器索引': item['container_index'],
                    '完整文本内容': item['text_content'],
                    'Li的CSS类': item['classes'],
                    'Li的Data属性': str(item['data_attributes']),
                    '产品标题': item.get('product_title', ''),
                    '产品价格': item.get('product_price', ''),
                    '产品价格文本': item.get('product_price_text', ''),
                    '产品描述': item.get('product_description', ''),
                    '产品品牌': item.get('product_brand', ''),
                    '包含链接数量': len(item['links']),
                    '包含图片数量': len(item['images']),
                    '第一个链接URL': item['links'][0]['url'] if item['links'] else '',
                    '第一个链接文本': item['links'][0]['text'] if item['links'] else '',
                    '第一个图片URL': item['images'][0]['src'] if item['images'] else '',
                    '第一个图片Alt': item['images'][0]['alt'] if item['images'] else '',
                    '完整HTML代码': item['inner_html']
                }
                detailed_data.append(main_row)

                # 详细链接数据 - 每个链接的完整信息
                for j, link in enumerate(item['links']):
                    links_data.append({
                        '所属Li索引': item['li_index'],
                        '所属Li文本': item['text_content'][:50] + '...' if len(item['text_content']) > 50 else item['text_content'],
                        '链接序号': j + 1,
                        '链接URL': link['url'],
                        '链接显示文本': link['text'],
                        '链接标题属性': link['title'],
                        '链接类型判断': self.classify_link(link['url']),
                        '是否外部链接': 'k3.cn' not in link['url'],
                        '链接长度': len(link['url']),
                        '文本长度': len(link['text'])
                    })

                # 详细图片数据 - 每个图片的完整信息
                for j, img in enumerate(item['images']):
                    images_data.append({
                        '所属Li索引': item['li_index'],
                        '所属Li文本': item['text_content'][:50] + '...' if len(item['text_content']) > 50 else item['text_content'],
                        '图片序号': j + 1,
                        '图片URL': img['src'],
                        '图片Alt文本': img['alt'],
                        '图片标题属性': img['title'],
                        '图片类型判断': self.classify_image(img['src']),
                        '是否外部图片': 'k3.cn' not in img['src'] if img['src'] else False,
                        '图片URL长度': len(img['src']) if img['src'] else 0
                    })

                # 所有子元素数据
                for child in item.get('child_elements', []):
                    all_elements_data.append({
                        '所属Li索引': item['li_index'],
                        '元素标签': child.get('tag', ''),
                        '元素文本': child.get('text', ''),
                        '元素类': child.get('classes', ''),
                        '元素属性': str(child.get('attributes', {}))
                    })

            # 保存到Excel的多个工作表
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 1. Li元素详细数据
                if detailed_data:
                    main_df = pd.DataFrame(detailed_data)
                    main_df.to_excel(writer, sheet_name='Li元素详细数据', index=False)

                # 2. 所有链接详细数据
                if links_data:
                    links_df = pd.DataFrame(links_data)
                    links_df.to_excel(writer, sheet_name='所有链接详细数据', index=False)

                # 3. 所有图片详细数据
                if images_data:
                    images_df = pd.DataFrame(images_data)
                    images_df.to_excel(writer, sheet_name='所有图片详细数据', index=False)

                # 4. 所有子元素数据
                if all_elements_data:
                    elements_df = pd.DataFrame(all_elements_data)
                    elements_df.to_excel(writer, sheet_name='所有子元素数据', index=False)

                # 5. 数据统计汇总
                stats_data = [{
                    '总Li元素数量': len(li_data_list),
                    '总链接数量': len(links_data),
                    '总图片数量': len(images_data),
                    '总子元素数量': len(all_elements_data),
                    '有链接的Li数量': len([item for item in li_data_list if item['links']]),
                    '有图片的Li数量': len([item for item in li_data_list if item['images']]),
                    '有产品信息的Li数量': len([item for item in li_data_list if item.get('product_title') or item.get('product_price')]),
                    '提取时间': time.strftime('%Y-%m-%d %H:%M:%S'),
                    '目标网址': 'https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html'
                }]
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='数据统计汇总', index=False)

            print(f"\n✅ 详细数据已保存到: {filename}")
            print(f"📊 数据统计:")
            print(f"   - 总Li元素: {len(li_data_list)} 个")
            print(f"   - 总链接: {len(links_data)} 个")
            print(f"   - 总图片: {len(images_data)} 张")
            print(f"   - 总子元素: {len(all_elements_data)} 个")
            print(f"   - 有链接的Li: {len([item for item in li_data_list if item['links']])} 个")
            print(f"   - 有图片的Li: {len([item for item in li_data_list if item['images']])} 个")

            return True

        except Exception as e:
            print(f"保存Excel文件时出错: {e}")
            return False

    def classify_link(self, url):
        """分类链接类型"""
        if not url:
            return '无链接'

        url_lower = url.lower()
        if 'login' in url_lower or 'auth' in url_lower:
            return '登录相关'
        elif 'api' in url_lower:
            return 'API接口'
        elif 'product' in url_lower or 'goods' in url_lower:
            return '产品相关'
        elif 'search' in url_lower:
            return '搜索相关'
        elif 'manage' in url_lower:
            return '管理相关'
        elif 'chat' in url_lower or 'service' in url_lower:
            return '客服相关'
        elif url_lower.startswith('javascript:'):
            return 'JavaScript'
        elif '#' in url:
            return '页面锚点'
        else:
            return '普通链接'

    def classify_image(self, src):
        """分类图片类型"""
        if not src:
            return '无图片'

        src_lower = src.lower()
        if any(ext in src_lower for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
            if 'logo' in src_lower:
                return 'Logo图片'
            elif 'icon' in src_lower:
                return '图标'
            elif 'product' in src_lower or 'goods' in src_lower:
                return '产品图片'
            elif 'avatar' in src_lower:
                return '头像'
            else:
                return '普通图片'
        else:
            return '其他资源'

def main():
    """主函数"""
    url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
    
    print("=== K3网站Content Li数据提取工具 ===\n")
    print(f"目标URL: {url}")
    
    extractor = ContentLiExtractor()
    
    # 提取数据
    li_data = extractor.process_url(url)
    
    if li_data:
        # 保存到Excel
        timestamp = int(time.time())
        filename = f'k3_content_li_data_{timestamp}.xlsx'
        success = extractor.save_to_excel(li_data, filename)
        
        if success:
            print(f"\n✅ 数据提取完成!")
            print(f"📁 文件已保存: {filename}")
        else:
            print(f"\n❌ 数据保存失败")
    else:
        print(f"\n❌ 未能提取到任何数据")
        print("可能的原因:")
        print("1. 页面需要登录")
        print("2. 页面结构与预期不符")
        print("3. 网络连接问题")

if __name__ == "__main__":
    main()
