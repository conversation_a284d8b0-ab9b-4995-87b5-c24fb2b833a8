import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
from urllib.parse import urljoin

class ContentLiExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.k3.cn/',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(self.headers)
        self.base_url = 'https://www.k3.cn'
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            print(f"页面访问成功，状态码: {response.status_code}")
            print(f"页面大小: {len(response.content)} 字节")
            return response
        except Exception as e:
            print(f"页面访问失败: {e}")
            return None
    
    def find_content_containers(self, soup):
        """查找所有可能的content容器"""
        print("\n=== 查找content容器 ===")
        
        # 查找不同的content类选择器
        selectors = [
            '.content',           # class="content"
            '[class*="content"]', # class包含content
            '#content',           # id="content"
            '.main-content',      # class="main-content"
            '.page-content',      # class="page-content"
            '.search-content'     # class="search-content"
        ]
        
        found_containers = []
        
        for selector in selectors:
            containers = soup.select(selector)
            if containers:
                print(f"选择器 '{selector}': 找到 {len(containers)} 个容器")
                for i, container in enumerate(containers):
                    li_elements = container.find_all('li')
                    if li_elements:
                        print(f"  容器 {i+1}: 包含 {len(li_elements)} 个 <li> 元素")
                        found_containers.append({
                            'selector': selector,
                            'container_index': i,
                            'container': container,
                            'li_count': len(li_elements)
                        })
        
        return found_containers
    
    def extract_li_data(self, li_element, index):
        """从单个li元素中提取数据"""
        data = {
            'li_index': index + 1,
            'text_content': '',
            'links': [],
            'images': [],
            'classes': '',
            'data_attributes': {},
            'inner_html': ''
        }
        
        try:
            # 提取文本内容
            data['text_content'] = li_element.get_text(strip=True)
            
            # 提取class属性
            classes = li_element.get('class', [])
            data['classes'] = ' '.join(classes) if classes else ''
            
            # 提取data属性
            for attr, value in li_element.attrs.items():
                if attr.startswith('data-'):
                    data['data_attributes'][attr] = value
            
            # 提取链接
            links = li_element.find_all('a', href=True)
            for link in links:
                href = link['href']
                if href.startswith('/'):
                    href = urljoin(self.base_url, href)
                
                data['links'].append({
                    'url': href,
                    'text': link.get_text(strip=True),
                    'title': link.get('title', '')
                })
            
            # 提取图片
            images = li_element.find_all('img', src=True)
            for img in images:
                src = img['src']
                if src.startswith('/'):
                    src = urljoin(self.base_url, src)
                
                data['images'].append({
                    'src': src,
                    'alt': img.get('alt', ''),
                    'title': img.get('title', '')
                })
            
            # 提取内部HTML (截取前500字符)
            data['inner_html'] = str(li_element)[:500]
            
            # 尝试提取结构化数据
            self.extract_structured_data(li_element, data)
            
        except Exception as e:
            print(f"提取li元素数据时出错: {e}")
            data['error'] = str(e)
        
        return data
    
    def extract_structured_data(self, li_element, data):
        """尝试提取结构化的产品数据"""
        # 查找可能的产品信息
        
        # 标题
        title_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.name', '.product-name', '[class*="title"]']
        for selector in title_selectors:
            title_elem = li_element.select_one(selector)
            if title_elem:
                data['product_title'] = title_elem.get_text(strip=True)
                break
        
        # 价格
        price_selectors = ['.price', '.cost', '.money', '[class*="price"]', '[class*="cost"]']
        for selector in price_selectors:
            price_elem = li_element.select_one(selector)
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                # 提取数字价格
                price_match = re.search(r'[\d,]+\.?\d*', price_text)
                if price_match:
                    data['product_price'] = price_match.group()
                data['product_price_text'] = price_text
                break
        
        # 描述
        desc_selectors = ['.desc', '.description', '.summary', '[class*="desc"]']
        for selector in desc_selectors:
            desc_elem = li_element.select_one(selector)
            if desc_elem:
                data['product_description'] = desc_elem.get_text(strip=True)
                break
        
        # 品牌/商家
        brand_selectors = ['.brand', '.shop', '.store', '[class*="brand"]', '[class*="shop"]']
        for selector in brand_selectors:
            brand_elem = li_element.select_one(selector)
            if brand_elem:
                data['product_brand'] = brand_elem.get_text(strip=True)
                break
    
    def process_url(self, url):
        """处理指定URL"""
        # 获取页面内容
        response = self.get_page_content(url)
        if not response:
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找content容器
        containers = self.find_content_containers(soup)
        
        if not containers:
            print("未找到任何content容器")
            return []
        
        all_li_data = []
        
        # 处理每个找到的容器
        for container_info in containers:
            print(f"\n=== 处理容器: {container_info['selector']} (第{container_info['container_index']+1}个) ===")
            
            container = container_info['container']
            li_elements = container.find_all('li')
            
            print(f"找到 {len(li_elements)} 个 <li> 元素")
            
            for i, li in enumerate(li_elements):
                print(f"处理第 {i+1} 个 <li> 元素...")
                
                li_data = self.extract_li_data(li, i)
                li_data['container_selector'] = container_info['selector']
                li_data['container_index'] = container_info['container_index']
                
                all_li_data.append(li_data)
                
                # 显示提取的数据预览
                if li_data['text_content']:
                    preview = li_data['text_content'][:100] + '...' if len(li_data['text_content']) > 100 else li_data['text_content']
                    print(f"  文本内容: {preview}")
                
                if li_data['links']:
                    print(f"  包含 {len(li_data['links'])} 个链接")
                
                if li_data['images']:
                    print(f"  包含 {len(li_data['images'])} 张图片")
        
        return all_li_data
    
    def save_to_excel(self, li_data_list, filename='content_li_data.xlsx'):
        """保存数据到Excel"""
        if not li_data_list:
            print("没有数据可保存")
            return False
        
        try:
            # 准备主数据表
            main_data = []
            links_data = []
            images_data = []
            
            for item in li_data_list:
                # 主数据
                main_row = {
                    'Li索引': item['li_index'],
                    '容器选择器': item['container_selector'],
                    '容器索引': item['container_index'],
                    '文本内容': item['text_content'],
                    'CSS类': item['classes'],
                    'Data属性': str(item['data_attributes']),
                    '产品标题': item.get('product_title', ''),
                    '产品价格': item.get('product_price', ''),
                    '产品价格文本': item.get('product_price_text', ''),
                    '产品描述': item.get('product_description', ''),
                    '产品品牌': item.get('product_brand', ''),
                    '链接数量': len(item['links']),
                    '图片数量': len(item['images']),
                    '内部HTML': item['inner_html']
                }
                main_data.append(main_row)
                
                # 链接数据
                for j, link in enumerate(item['links']):
                    links_data.append({
                        'Li索引': item['li_index'],
                        '链接索引': j + 1,
                        'URL': link['url'],
                        '链接文本': link['text'],
                        '链接标题': link['title']
                    })
                
                # 图片数据
                for j, img in enumerate(item['images']):
                    images_data.append({
                        'Li索引': item['li_index'],
                        '图片索引': j + 1,
                        '图片URL': img['src'],
                        '图片Alt': img['alt'],
                        '图片标题': img['title']
                    })
            
            # 保存到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 主数据表
                main_df = pd.DataFrame(main_data)
                main_df.to_excel(writer, sheet_name='Li元素数据', index=False)
                
                # 链接数据表
                if links_data:
                    links_df = pd.DataFrame(links_data)
                    links_df.to_excel(writer, sheet_name='链接数据', index=False)
                
                # 图片数据表
                if images_data:
                    images_df = pd.DataFrame(images_data)
                    images_df.to_excel(writer, sheet_name='图片数据', index=False)
                
                # 统计信息
                stats_data = [{
                    '总Li元素数量': len(li_data_list),
                    '总链接数量': len(links_data),
                    '总图片数量': len(images_data),
                    '提取时间': time.strftime('%Y-%m-%d %H:%M:%S')
                }]
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            print(f"\n数据已保存到: {filename}")
            print(f"总共提取了 {len(li_data_list)} 个 <li> 元素")
            print(f"包含 {len(links_data)} 个链接")
            print(f"包含 {len(images_data)} 张图片")
            
            return True
            
        except Exception as e:
            print(f"保存Excel文件时出错: {e}")
            return False

def main():
    """主函数"""
    url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
    
    print("=== K3网站Content Li数据提取工具 ===\n")
    print(f"目标URL: {url}")
    
    extractor = ContentLiExtractor()
    
    # 提取数据
    li_data = extractor.process_url(url)
    
    if li_data:
        # 保存到Excel
        timestamp = int(time.time())
        filename = f'k3_content_li_data_{timestamp}.xlsx'
        success = extractor.save_to_excel(li_data, filename)
        
        if success:
            print(f"\n✅ 数据提取完成!")
            print(f"📁 文件已保存: {filename}")
        else:
            print(f"\n❌ 数据保存失败")
    else:
        print(f"\n❌ 未能提取到任何数据")
        print("可能的原因:")
        print("1. 页面需要登录")
        print("2. 页面结构与预期不符")
        print("3. 网络连接问题")

if __name__ == "__main__":
    main()
