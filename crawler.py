import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import urllib.parse
import os

# 尝试导入Selenium，如果不可用则跳过
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Selenium不可用，将只使用requests进行抓取")

class K3WebScraper:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)

    def setup_selenium_driver(self):
        """设置Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            print("Selenium不可用，无法使用浏览器模式")
            return None

        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={self.headers["User-Agent"]}')

            driver = webdriver.Chrome(options=chrome_options)
            return driver
        except Exception as e:
            print(f"无法启动Chrome浏览器: {e}")
            print("请确保已安装Chrome浏览器和ChromeDriver")
            return None

    def scrape_with_requests(self, url):
        """使用requests库尝试抓取数据"""
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # 检查是否需要登录
            if '登录' in response.text or 'login' in response.text.lower():
                print("检测到需要登录，将尝试使用Selenium...")
                return None

            return soup

        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None

    def scrape_with_selenium(self, url):
        """使用Selenium抓取需要JavaScript的页面"""
        driver = self.setup_selenium_driver()
        if not driver:
            return None

        try:
            print(f"使用Selenium访问: {url}")
            driver.get(url)

            # 等待页面加载
            time.sleep(3)

            # 检查是否有登录要求
            page_source = driver.page_source
            if '登录' in page_source:
                print("页面需要登录，无法直接访问搜索结果")
                return None

            soup = BeautifulSoup(page_source, 'html.parser')
            return soup

        except Exception as e:
            print(f"Selenium抓取失败: {e}")
            return None
        finally:
            if driver:
                driver.quit()

    def extract_product_data(self, soup):
        """从页面中提取产品数据"""
        products = []

        if not soup:
            return products

        # 尝试多种可能的产品容器选择器
        selectors = [
            '.product-item',
            '.item',
            '.goods-item',
            '.search-item',
            '.result-item',
            '[class*="product"]',
            '[class*="item"]'
        ]

        for selector in selectors:
            items = soup.select(selector)
            if items:
                print(f"找到 {len(items)} 个产品项目 (使用选择器: {selector})")
                break
        else:
            # 如果没有找到标准的产品容器，尝试提取所有链接和文本
            print("未找到标准产品容器，尝试提取页面中的所有有用信息...")
            items = soup.find_all(['div', 'li', 'article'], class_=True)

        for item in items:
            try:
                product = {}

                # 提取标题
                title_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.name', '[class*="title"]', '[class*="name"]']
                title = None
                for sel in title_selectors:
                    title_elem = item.select_one(sel)
                    if title_elem and title_elem.get_text(strip=True):
                        title = title_elem.get_text(strip=True)
                        break

                # 提取价格
                price_selectors = ['.price', '.cost', '[class*="price"]', '[class*="cost"]', '[class*="money"]']
                price = None
                for sel in price_selectors:
                    price_elem = item.select_one(sel)
                    if price_elem and price_elem.get_text(strip=True):
                        price = price_elem.get_text(strip=True)
                        break

                # 提取链接
                link = None
                link_elem = item.find('a', href=True)
                if link_elem:
                    link = link_elem['href']
                    if link.startswith('/'):
                        link = 'https://www.k3.cn' + link

                # 提取图片
                image = None
                img_elem = item.find('img', src=True)
                if img_elem:
                    image = img_elem['src']
                    if image.startswith('/'):
                        image = 'https://www.k3.cn' + image

                # 提取描述
                description = None
                desc_selectors = ['.desc', '.description', '[class*="desc"]', 'p']
                for sel in desc_selectors:
                    desc_elem = item.select_one(sel)
                    if desc_elem and desc_elem.get_text(strip=True):
                        description = desc_elem.get_text(strip=True)
                        break

                # 只有当至少有标题或链接时才添加产品
                if title or link:
                    product = {
                        '标题': title or '未找到标题',
                        '价格': price or '未找到价格',
                        '链接': link or '未找到链接',
                        '图片': image or '未找到图片',
                        '描述': description or '未找到描述'
                    }
                    products.append(product)

            except Exception as e:
                print(f"提取产品信息时出错: {e}")
                continue

        return products

    def save_to_excel(self, products, filename='运动鞋数据.xlsx'):
        """将产品数据保存到Excel文件"""
        if not products:
            print("没有数据可保存")
            return False

        try:
            df = pd.DataFrame(products)
            df.to_excel(filename, index=False, engine='openpyxl')
            print(f"数据已保存到 {filename}")
            print(f"共保存了 {len(products)} 条记录")
            return True
        except Exception as e:
            print(f"保存Excel文件时出错: {e}")
            return False

    def scrape_k3_search(self, url):
        """主要的抓取方法"""
        print("开始抓取K3网站数据...")

        # 首先尝试使用requests
        soup = self.scrape_with_requests(url)

        # 如果requests失败，尝试使用Selenium
        if not soup:
            soup = self.scrape_with_selenium(url)

        if not soup:
            print("无法获取页面内容，可能需要登录或网站有反爬虫措施")
            return []

        # 提取产品数据
        products = self.extract_product_data(soup)

        if not products:
            print("未找到产品数据，尝试提取页面中的所有有用信息...")
            # 如果没有找到产品，尝试提取页面中的所有链接和文本作为备用
            all_links = soup.find_all('a', href=True)
            for link in all_links[:20]:  # 限制前20个链接
                text = link.get_text(strip=True)
                href = link['href']
                if text and len(text) > 5:  # 过滤掉太短的文本
                    if href.startswith('/'):
                        href = 'https://www.k3.cn' + href
                    products.append({
                        '标题': text,
                        '价格': '未找到价格',
                        '链接': href,
                        '图片': '未找到图片',
                        '描述': '从链接提取的文本'
                    })

        return products

def main():
    """主函数"""
    # 创建爬虫实例
    scraper = K3WebScraper()

    # 尝试多个URL
    urls_to_try = [
        "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html",
        "https://www.k3.cn/",  # 主页
        "https://www.k3.cn/thread.html",  # 供求信息页面
    ]

    products = []

    for url in urls_to_try:
        print(f"\n尝试访问: {url}")
        current_products = scraper.scrape_k3_search(url)
        if current_products:
            products.extend(current_products)
            print(f"从 {url} 提取到 {len(current_products)} 条数据")
            break
        else:
            print(f"从 {url} 未能提取到数据")

    if products:
        print(f"\n总共成功提取了 {len(products)} 条数据")

        # 显示前几条数据作为预览
        print("\n数据预览:")
        for i, product in enumerate(products[:3]):
            print(f"\n第 {i+1} 条:")
            for key, value in product.items():
                print(f"  {key}: {value}")

        # 保存到Excel
        success = scraper.save_to_excel(products)
        if success:
            print(f"\n数据已成功保存到Excel文件")
        else:
            print("\n保存Excel文件失败")
    else:
        print("\n未能提取到任何数据")
        print("可能的原因:")
        print("1. 网站需要登录")
        print("2. 网站有反爬虫措施")
        print("3. 页面结构发生了变化")
        print("4. 网络连接问题")
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 尝试手动访问网站确认是否需要登录")
        print("3. 考虑使用其他数据源")

if __name__ == "__main__":
    main()