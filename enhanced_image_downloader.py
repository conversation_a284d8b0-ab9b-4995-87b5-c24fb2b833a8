import requests
from bs4 import BeautifulSoup
import os
import time
from urllib.parse import urljoin, urlparse
import re
import json

class EnhancedImageDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.k3.cn/',
        }
        self.session.headers.update(self.headers)
        self.base_url = 'https://www.k3.cn'
        self.images_folder = 'k3_all_images'
        self.create_images_folder()
        
    def create_images_folder(self):
        """创建图片下载文件夹"""
        timestamp = int(time.time())
        self.images_folder = f'k3_images_{timestamp}'
        
        if not os.path.exists(self.images_folder):
            os.makedirs(self.images_folder)
            print(f"创建图片文件夹: {self.images_folder}")
        
        # 创建子文件夹分类保存
        subfolders = ['successful', 'failed_info', 'thumbnails', 'icons', 'backgrounds']
        for folder in subfolders:
            subfolder_path = os.path.join(self.images_folder, folder)
            if not os.path.exists(subfolder_path):
                os.makedirs(subfolder_path)
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问页面: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            print(f"页面访问成功，状态码: {response.status_code}")
            return response
        except Exception as e:
            print(f"页面访问失败: {e}")
            return None
    
    def extract_all_images_comprehensive(self, url):
        """全面提取页面中的所有图片"""
        response = self.get_page_content(url)
        if not response:
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        all_images = []
        
        print("\n=== 全面提取图片 ===")
        
        # 1. 提取img标签
        img_images = self.extract_img_tags(soup, url)
        all_images.extend(img_images)
        
        # 2. 提取CSS背景图片
        css_images = self.extract_css_images(soup, url)
        all_images.extend(css_images)
        
        # 3. 提取JavaScript中的图片
        js_images = self.extract_javascript_images(soup, url)
        all_images.extend(js_images)
        
        # 4. 提取外部CSS文件中的图片
        external_css_images = self.extract_external_css_images(soup, url)
        all_images.extend(external_css_images)
        
        # 5. 提取图标和favicon
        icon_images = self.extract_icons(soup, url)
        all_images.extend(icon_images)
        
        # 6. 尝试从常见路径获取图片
        common_images = self.extract_common_path_images(url)
        all_images.extend(common_images)
        
        # 去重并分类
        unique_images = self.process_and_classify_images(all_images)
        
        print(f"\n总共找到 {len(all_images)} 个图片引用")
        print(f"去重后剩余 {len(unique_images)} 个唯一图片")
        
        return unique_images
    
    def extract_img_tags(self, soup, base_url):
        """提取img标签中的图片"""
        images = []
        img_tags = soup.find_all('img')
        print(f"找到 {len(img_tags)} 个 <img> 标签")
        
        for i, img in enumerate(img_tags, 1):
            # 提取各种可能的图片属性
            attributes = ['src', 'data-src', 'data-original', 'data-lazy', 'data-url']
            
            for attr in attributes:
                src = img.get(attr, '')
                if src:
                    full_url = self.normalize_url(src, base_url)
                    if full_url:
                        images.append({
                            'url': full_url,
                            'original_src': src,
                            'alt': img.get('alt', ''),
                            'title': img.get('title', ''),
                            'source': f'img_tag_{i}_{attr}',
                            'type': 'img_tag',
                            'category': self.classify_image_type(full_url, img.get('alt', ''))
                        })
        
        return images
    
    def extract_css_images(self, soup, base_url):
        """提取CSS中的图片"""
        images = []
        
        # 内联CSS
        style_tags = soup.find_all('style')
        for i, style in enumerate(style_tags, 1):
            if style.string:
                css_images = self.parse_css_for_images(style.string, f'inline_css_{i}', base_url)
                images.extend(css_images)
        
        # 内联样式
        elements_with_style = soup.find_all(style=True)
        for i, element in enumerate(elements_with_style, 1):
            style_content = element.get('style', '')
            css_images = self.parse_css_for_images(style_content, f'inline_style_{i}', base_url)
            images.extend(css_images)
        
        return images
    
    def parse_css_for_images(self, css_content, source, base_url):
        """解析CSS内容中的图片"""
        images = []
        
        # 各种CSS图片引用模式
        patterns = [
            r'background-image:\s*url\(["\']?([^"\')\s]+)["\']?\)',
            r'background:\s*[^;]*url\(["\']?([^"\')\s]+)["\']?\)',
            r'content:\s*url\(["\']?([^"\')\s]+)["\']?\)',
            r'list-style-image:\s*url\(["\']?([^"\')\s]+)["\']?\)',
            r'border-image:\s*url\(["\']?([^"\')\s]+)["\']?\)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, css_content, re.IGNORECASE)
            for j, img_url in enumerate(matches, 1):
                full_url = self.normalize_url(img_url, base_url)
                if full_url:
                    images.append({
                        'url': full_url,
                        'original_src': img_url,
                        'alt': '',
                        'title': '',
                        'source': f'{source}_css_{j}',
                        'type': 'css_image',
                        'category': self.classify_image_type(full_url, '')
                    })
        
        return images
    
    def extract_javascript_images(self, soup, base_url):
        """提取JavaScript中的图片"""
        images = []
        script_tags = soup.find_all('script')
        
        for i, script in enumerate(script_tags, 1):
            if script.string:
                # 查找JavaScript中的图片URL
                js_patterns = [
                    r'["\']([^"\']*\.(?:jpg|jpeg|png|gif|webp|svg|ico))["\']',
                    r'src\s*[:=]\s*["\']([^"\']+)["\']',
                    r'image\s*[:=]\s*["\']([^"\']+)["\']',
                    r'url\s*[:=]\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in js_patterns:
                    matches = re.findall(pattern, script.string, re.IGNORECASE)
                    for j, img_url in enumerate(matches, 1):
                        full_url = self.normalize_url(img_url, base_url)
                        if full_url and self.is_image_url(full_url):
                            images.append({
                                'url': full_url,
                                'original_src': img_url,
                                'alt': '',
                                'title': '',
                                'source': f'javascript_{i}_{j}',
                                'type': 'javascript_image',
                                'category': self.classify_image_type(full_url, '')
                            })
        
        return images
    
    def extract_external_css_images(self, soup, base_url):
        """提取外部CSS文件中的图片"""
        images = []
        link_tags = soup.find_all('link', rel='stylesheet')
        
        for i, link in enumerate(link_tags, 1):
            href = link.get('href', '')
            if href:
                css_url = self.normalize_url(href, base_url)
                if css_url:
                    try:
                        print(f"  正在获取外部CSS: {css_url}")
                        response = self.session.get(css_url, timeout=10)
                        if response.status_code == 200:
                            css_images = self.parse_css_for_images(
                                response.text, f'external_css_{i}', css_url
                            )
                            images.extend(css_images)
                    except Exception as e:
                        print(f"  获取外部CSS失败: {e}")
        
        return images
    
    def extract_icons(self, soup, base_url):
        """提取图标"""
        images = []
        
        # favicon和各种图标
        icon_selectors = [
            'link[rel="icon"]',
            'link[rel="shortcut icon"]',
            'link[rel="apple-touch-icon"]',
            'link[rel="apple-touch-icon-precomposed"]',
            'meta[property="og:image"]',
            'meta[name="twitter:image"]'
        ]
        
        for selector in icon_selectors:
            elements = soup.select(selector)
            for i, element in enumerate(elements, 1):
                href = element.get('href') or element.get('content', '')
                if href:
                    full_url = self.normalize_url(href, base_url)
                    if full_url:
                        images.append({
                            'url': full_url,
                            'original_src': href,
                            'alt': '',
                            'title': '',
                            'source': f'icon_{selector}_{i}',
                            'type': 'icon',
                            'category': 'icon'
                        })
        
        return images
    
    def extract_common_path_images(self, base_url):
        """尝试从常见路径获取图片"""
        images = []
        common_paths = [
            '/favicon.ico',
            '/apple-touch-icon.png',
            '/logo.png',
            '/logo.jpg',
            '/images/logo.png',
            '/img/logo.png',
            '/static/logo.png',
            '/assets/logo.png'
        ]
        
        for path in common_paths:
            full_url = urljoin(self.base_url, path)
            images.append({
                'url': full_url,
                'original_src': path,
                'alt': '',
                'title': '',
                'source': f'common_path_{path.replace("/", "_")}',
                'type': 'common_path',
                'category': 'icon' if 'icon' in path or 'favicon' in path else 'logo'
            })
        
        return images
    
    def classify_image_type(self, url, alt_text):
        """分类图片类型"""
        url_lower = url.lower()
        alt_lower = alt_text.lower()
        
        if any(keyword in url_lower for keyword in ['icon', 'favicon']):
            return 'icon'
        elif any(keyword in url_lower for keyword in ['logo', 'brand']):
            return 'logo'
        elif any(keyword in url_lower for keyword in ['thumb', 'small', 'mini']):
            return 'thumbnail'
        elif any(keyword in url_lower for keyword in ['bg', 'background']):
            return 'background'
        elif any(keyword in alt_lower for keyword in ['产品', 'product', '商品']):
            return 'product'
        else:
            return 'general'
    
    def normalize_url(self, src, base_url):
        """标准化URL"""
        if not src or src.startswith(('data:', 'javascript:', '#')):
            return None
        
        src = src.strip()
        
        if src.startswith('//'):
            return 'https:' + src
        elif src.startswith('/'):
            return urljoin(self.base_url, src)
        elif not src.startswith('http'):
            return urljoin(base_url, src)
        else:
            return src
    
    def is_image_url(self, url):
        """检查URL是否可能是图片"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.ico', '.bmp']
        url_lower = url.lower()
        return any(ext in url_lower for ext in image_extensions)
    
    def process_and_classify_images(self, image_list):
        """处理和分类图片"""
        # 去重
        seen_urls = set()
        unique_images = []
        
        for img in image_list:
            if img['url'] not in seen_urls:
                seen_urls.add(img['url'])
                unique_images.append(img)
        
        # 按类别分组
        categories = {}
        for img in unique_images:
            category = img['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(img)
        
        print(f"\n图片分类统计:")
        for category, imgs in categories.items():
            print(f"  - {category}: {len(imgs)} 张")
        
        return unique_images
    
    def download_image_enhanced(self, image_info, index):
        """增强版图片下载"""
        url = image_info['url']
        category = image_info['category']
        
        try:
            print(f"正在下载图片 {index}: {url}")
            
            response = self.session.get(url, timeout=15, stream=True)
            response.raise_for_status()
            
            # 检查内容类型
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                print(f"  跳过非图片文件: {content_type}")
                return None
            
            # 确定保存文件夹
            if category in ['icon', 'logo']:
                save_folder = os.path.join(self.images_folder, 'icons')
            elif category == 'thumbnail':
                save_folder = os.path.join(self.images_folder, 'thumbnails')
            elif category == 'background':
                save_folder = os.path.join(self.images_folder, 'backgrounds')
            else:
                save_folder = os.path.join(self.images_folder, 'successful')
            
            # 生成文件名
            ext = self.get_file_extension(url, content_type)
            filename = self.generate_safe_filename(url, image_info['source'], category, ext, index)
            filepath = os.path.join(save_folder, filename)
            
            # 下载并保存
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"  下载成功: {filename} ({file_size} 字节) -> {category}")
            
            return {
                'filename': filename,
                'filepath': filepath,
                'url': url,
                'size': file_size,
                'category': category,
                'type': image_info['type'],
                'source': image_info['source']
            }
            
        except Exception as e:
            print(f"  下载失败: {e}")
            
            # 保存失败信息
            self.save_failed_info(image_info, str(e), index)
            return None
    
    def save_failed_info(self, image_info, error, index):
        """保存失败的图片信息"""
        failed_info_file = os.path.join(self.images_folder, 'failed_info', f'failed_{index:03d}.json')
        
        failed_data = {
            'index': index,
            'url': image_info['url'],
            'original_src': image_info['original_src'],
            'source': image_info['source'],
            'type': image_info['type'],
            'category': image_info['category'],
            'error': error,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open(failed_info_file, 'w', encoding='utf-8') as f:
            json.dump(failed_data, f, ensure_ascii=False, indent=2)
    
    def get_file_extension(self, url, content_type):
        """获取文件扩展名"""
        if 'jpeg' in content_type or 'jpg' in content_type:
            return '.jpg'
        elif 'png' in content_type:
            return '.png'
        elif 'gif' in content_type:
            return '.gif'
        elif 'webp' in content_type:
            return '.webp'
        elif 'svg' in content_type:
            return '.svg'
        elif 'ico' in content_type:
            return '.ico'
        
        # 从URL获取
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()
        if path.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.ico')):
            return os.path.splitext(path)[1]
        
        return '.jpg'
    
    def generate_safe_filename(self, url, source, category, ext, index):
        """生成安全的文件名"""
        parsed_url = urlparse(url)
        original_name = os.path.basename(parsed_url.path)
        
        if original_name and '.' in original_name:
            name_part = os.path.splitext(original_name)[0]
        else:
            name_part = f"{category}_{index}"
        
        safe_name = re.sub(r'[^\w\-_.]', '_', name_part)
        safe_source = re.sub(r'[^\w\-_.]', '_', source)
        
        filename = f"{index:03d}_{safe_name}_{category}_{safe_source}{ext}"
        
        if len(filename) > 200:
            filename = f"{index:03d}_{category}_{safe_source[:50]}{ext}"
        
        return filename
    
    def download_all_images_enhanced(self, url):
        """增强版下载所有图片"""
        print("=== K3网站增强版图片下载器 ===\n")
        print(f"目标URL: {url}")
        
        # 提取所有图片
        image_list = self.extract_all_images_comprehensive(url)
        
        if not image_list:
            print("未找到任何图片")
            return []
        
        print(f"\n=== 开始下载 {len(image_list)} 张图片 ===")
        
        downloaded_images = []
        failed_count = 0
        
        for i, image_info in enumerate(image_list, 1):
            print(f"\n[{i}/{len(image_list)}]")
            result = self.download_image_enhanced(image_info, i)
            
            if result:
                downloaded_images.append(result)
            else:
                failed_count += 1
            
            time.sleep(0.3)  # 减少延时
        
        # 生成详细报告
        self.generate_comprehensive_report(downloaded_images, failed_count, image_list)
        
        return downloaded_images
    
    def generate_comprehensive_report(self, downloaded_images, failed_count, total_images):
        """生成综合报告"""
        report_file = os.path.join(self.images_folder, 'comprehensive_report.txt')
        
        # 按类别统计
        category_stats = {}
        for img in downloaded_images:
            category = img['category']
            if category not in category_stats:
                category_stats[category] = {'count': 0, 'total_size': 0}
            category_stats[category]['count'] += 1
            category_stats[category]['total_size'] += img['size']
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=== K3网站图片下载综合报告 ===\n\n")
            f.write(f"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"目标网址: https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html\n")
            f.write(f"保存位置: {self.images_folder}\n\n")
            
            f.write("=== 下载统计 ===\n")
            f.write(f"总图片数量: {len(total_images)}\n")
            f.write(f"成功下载: {len(downloaded_images)}\n")
            f.write(f"下载失败: {failed_count}\n")
            f.write(f"成功率: {len(downloaded_images)/len(total_images)*100:.1f}%\n\n")
            
            f.write("=== 分类统计 ===\n")
            for category, stats in category_stats.items():
                f.write(f"{category}: {stats['count']} 张, {stats['total_size']} 字节\n")
            
            f.write(f"\n=== 成功下载的图片详情 ===\n")
            for img in downloaded_images:
                f.write(f"文件名: {img['filename']}\n")
                f.write(f"原始URL: {img['url']}\n")
                f.write(f"文件大小: {img['size']} 字节\n")
                f.write(f"分类: {img['category']}\n")
                f.write(f"类型: {img['type']}\n")
                f.write(f"来源: {img['source']}\n")
                f.write("-" * 50 + "\n")
        
        print(f"\n=== 增强版下载完成 ===")
        print(f"📁 图片保存位置: {self.images_folder}")
        print(f"📊 下载统计:")
        print(f"   - 总图片数量: {len(total_images)}")
        print(f"   - 成功下载: {len(downloaded_images)}")
        print(f"   - 下载失败: {failed_count}")
        print(f"   - 成功率: {len(downloaded_images)/len(total_images)*100:.1f}%")
        print(f"📄 详细报告: {report_file}")
        
        if category_stats:
            print(f"📂 分类统计:")
            for category, stats in category_stats.items():
                print(f"   - {category}: {stats['count']} 张")

def main():
    """主函数"""
    url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
    
    downloader = EnhancedImageDownloader()
    downloaded_images = downloader.download_all_images_enhanced(url)
    
    if downloaded_images:
        print(f"\n✅ 增强版图片下载完成!")
        print(f"📁 请查看文件夹: {downloader.images_folder}")
    else:
        print(f"\n❌ 没有成功下载任何图片")

if __name__ == "__main__":
    main()
