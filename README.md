# K3网站运动鞋数据抓取工具

这是一个用于从K3网站抓取运动鞋相关数据并保存到Excel文件的Python工具。

## 功能特点

- 🔍 多策略数据抓取：尝试多种方法获取数据
- 📊 Excel导出：将数据保存为Excel文件，支持多工作表
- 🛡️ 错误处理：完善的异常处理机制
- 📝 详细日志：显示抓取过程和结果
- 🎯 智能过滤：自动识别鞋类相关内容

## 文件说明

### 主要文件

1. **k3_scraper_final.py** - 最终完整版爬虫
   - 综合多种抓取策略
   - 包含示例数据
   - 支持多工作表Excel导出

2. **demo_scraper.py** - 演示版爬虫
   - 展示基本功能
   - 包含示例数据创建
   - 简单的网页抓取演示

3. **crawler.py** - 基础版爬虫
   - 支持Selenium（可选）
   - 基本的网页抓取功能

### 配置文件

- **requirements.txt** - Python依赖包列表
- **README.md** - 使用说明文档

## 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖包：
- requests - HTTP请求库
- beautifulsoup4 - HTML解析库
- pandas - 数据处理库
- openpyxl - Excel文件操作库

## 使用方法

### 1. 运行完整版爬虫

```bash
python k3_scraper_final.py
```

这将：
- 尝试从K3主页抓取鞋类相关链接
- 从其他测试数据源获取数据
- 添加示例运动鞋数据
- 将所有数据保存到 `运动鞋数据_完整版.xlsx`

### 2. 运行演示版

```bash
python demo_scraper.py
```

这将：
- 创建示例运动鞋数据
- 演示简单的网页抓取
- 保存到 `运动鞋数据_示例.xlsx` 和 `网页抓取_示例.xlsx`

### 3. 运行基础版

```bash
python crawler.py
```

尝试直接抓取K3搜索页面（可能需要登录）

## 输出文件

生成的Excel文件包含以下列：
- **标题** - 产品名称
- **价格** - 产品价格
- **链接** - 产品链接
- **图片** - 产品图片URL
- **描述** - 产品描述
- **来源** - 数据来源

## 注意事项

1. **网站限制**：K3网站的搜索功能需要登录，直接抓取可能受限
2. **网络连接**：确保网络连接正常
3. **反爬虫**：网站可能有反爬虫措施，请合理使用
4. **数据准确性**：抓取的数据仅供参考，实际信息请以官网为准

## 故障排除

### 常见问题

1. **无法抓取数据**
   - 检查网络连接
   - 确认网站是否需要登录
   - 查看控制台错误信息

2. **Excel保存失败**
   - 确保安装了openpyxl包
   - 检查文件权限
   - 确保文件未被其他程序占用

3. **依赖包安装失败**
   - 尝试使用国内镜像源
   - 检查Python版本兼容性

### 解决方案

```bash
# 使用国内镜像安装依赖
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 单独安装问题包
pip install pandas openpyxl beautifulsoup4 requests
```

## 扩展功能

可以根据需要扩展以下功能：
- 添加更多数据源
- 实现登录功能
- 增加数据清洗和验证
- 支持更多输出格式（CSV、JSON等）
- 添加定时抓取功能

## 免责声明

本工具仅用于学习和研究目的，请遵守网站的robots.txt和使用条款。使用者应对使用本工具产生的任何后果负责。

## 版本信息

- 版本：1.0
- 更新日期：2025-01-14
- Python版本要求：3.6+
