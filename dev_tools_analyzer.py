import requests
from bs4 import BeautifulSoup
import json
import re
import pandas as pd
from urllib.parse import urljoin, urlparse, parse_qs
import time

class DevToolsAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(self.headers)
    
    def analyze_network_requests(self, url):
        """分析网络请求"""
        print(f"正在分析网页: {url}")
        
        try:
            response = self.session.get(url, timeout=15)
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应头信息:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            
            # 分析响应内容
            content_type = response.headers.get('content-type', '')
            print(f"\n内容类型: {content_type}")
            print(f"响应大小: {len(response.content)} 字节")
            
            return response
            
        except Exception as e:
            print(f"请求失败: {e}")
            return None
    
    def extract_javascript_data(self, response):
        """提取JavaScript中的数据"""
        if not response:
            return {}
        
        soup = BeautifulSoup(response.content, 'html.parser')
        js_data = {}
        
        # 查找所有script标签
        scripts = soup.find_all('script')
        print(f"\n找到 {len(scripts)} 个JavaScript脚本")
        
        for i, script in enumerate(scripts):
            script_content = script.string
            if script_content:
                # 查找JSON数据
                json_patterns = [
                    r'var\s+\w+\s*=\s*(\{.*?\});',
                    r'window\.\w+\s*=\s*(\{.*?\});',
                    r'data\s*:\s*(\{.*?\})',
                    r'config\s*:\s*(\{.*?\})',
                    r'__INITIAL_STATE__\s*=\s*(\{.*?\});'
                ]
                
                for pattern in json_patterns:
                    matches = re.findall(pattern, script_content, re.DOTALL)
                    for match in matches:
                        try:
                            data = json.loads(match)
                            js_data[f'script_{i}_data'] = data
                            print(f"  脚本 {i}: 找到JSON数据")
                        except:
                            pass
        
        return js_data
    
    def extract_api_endpoints(self, response):
        """提取API端点"""
        if not response:
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        api_endpoints = []
        
        # 在JavaScript中查找API URL
        scripts = soup.find_all('script')
        
        api_patterns = [
            r'["\']https?://[^"\']*api[^"\']*["\']',
            r'["\']https?://[^"\']*search[^"\']*["\']',
            r'["\']https?://[^"\']*data[^"\']*["\']',
            r'["\']\/api\/[^"\']*["\']',
            r'["\']\/search\/[^"\']*["\']'
        ]
        
        for script in scripts:
            if script.string:
                for pattern in api_patterns:
                    matches = re.findall(pattern, script.string)
                    for match in matches:
                        clean_url = match.strip('"\'')
                        if clean_url not in api_endpoints:
                            api_endpoints.append(clean_url)
        
        return api_endpoints
    
    def extract_form_data(self, response):
        """提取表单数据"""
        if not response:
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        forms = soup.find_all('form')
        
        form_data = []
        for i, form in enumerate(forms):
            form_info = {
                'form_index': i,
                'action': form.get('action', ''),
                'method': form.get('method', 'GET'),
                'inputs': []
            }
            
            inputs = form.find_all(['input', 'select', 'textarea'])
            for inp in inputs:
                input_info = {
                    'type': inp.get('type', inp.name),
                    'name': inp.get('name', ''),
                    'value': inp.get('value', ''),
                    'placeholder': inp.get('placeholder', '')
                }
                form_info['inputs'].append(input_info)
            
            form_data.append(form_info)
        
        return form_data
    
    def extract_ajax_requests(self, response):
        """提取AJAX请求信息"""
        if not response:
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        ajax_requests = []
        
        scripts = soup.find_all('script')
        
        ajax_patterns = [
            r'\.ajax\s*\(\s*\{([^}]+)\}',
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'XMLHttpRequest.*?open\s*\(\s*["\'](\w+)["\'],\s*["\']([^"\']+)["\']'
        ]
        
        for script in scripts:
            if script.string:
                for pattern in ajax_patterns:
                    matches = re.findall(pattern, script.string)
                    ajax_requests.extend(matches)
        
        return ajax_requests
    
    def analyze_page_structure(self, response):
        """分析页面结构"""
        if not response:
            return {}
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        structure = {
            'title': soup.title.string if soup.title else '',
            'meta_tags': [],
            'links': [],
            'images': [],
            'divs_with_class': [],
            'data_attributes': []
        }
        
        # Meta标签
        for meta in soup.find_all('meta'):
            meta_info = {}
            for attr in ['name', 'property', 'content', 'charset']:
                if meta.get(attr):
                    meta_info[attr] = meta.get(attr)
            if meta_info:
                structure['meta_tags'].append(meta_info)
        
        # 链接
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text(strip=True)
            if text and len(text) < 100:  # 避免太长的文本
                structure['links'].append({'href': href, 'text': text})
        
        # 图片
        for img in soup.find_all('img', src=True):
            structure['images'].append({
                'src': img['src'],
                'alt': img.get('alt', ''),
                'title': img.get('title', '')
            })
        
        # 带class的div
        for div in soup.find_all('div', class_=True):
            classes = ' '.join(div.get('class', []))
            if classes:
                structure['divs_with_class'].append(classes)
        
        # data属性
        for element in soup.find_all():
            if hasattr(element, 'attrs') and element.attrs:
                data_attrs = {k: v for k, v in element.attrs.items() if k.startswith('data-')}
                if data_attrs:
                    structure['data_attributes'].append({
                        'tag': element.name,
                        'attributes': data_attrs
                    })
        
        return structure
    
    def save_analysis_results(self, url, response, js_data, api_endpoints, form_data, ajax_requests, structure):
        """保存分析结果"""
        results = {
            'url': url,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'response_info': {
                'status_code': response.status_code if response else None,
                'headers': dict(response.headers) if response else {},
                'content_length': len(response.content) if response else 0
            },
            'javascript_data': js_data,
            'api_endpoints': api_endpoints,
            'form_data': form_data,
            'ajax_requests': ajax_requests,
            'page_structure': structure
        }
        
        # 保存为JSON文件
        filename = f'dev_tools_analysis_{int(time.time())}.json'
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析结果已保存到: {filename}")
        
        # 创建Excel报告
        self.create_excel_report(results)
        
        return filename
    
    def create_excel_report(self, results):
        """创建Excel报告"""
        filename = f'dev_tools_report_{int(time.time())}.xlsx'
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 基本信息
            basic_info = pd.DataFrame([{
                'URL': results['url'],
                '分析时间': results['timestamp'],
                'HTTP状态码': results['response_info']['status_code'],
                '内容大小': results['response_info']['content_length']
            }])
            basic_info.to_excel(writer, sheet_name='基本信息', index=False)
            
            # API端点
            if results['api_endpoints']:
                api_df = pd.DataFrame([{'API端点': endpoint} for endpoint in results['api_endpoints']])
                api_df.to_excel(writer, sheet_name='API端点', index=False)
            
            # 表单数据
            if results['form_data']:
                form_list = []
                for form in results['form_data']:
                    for inp in form['inputs']:
                        form_list.append({
                            '表单索引': form['form_index'],
                            '表单动作': form['action'],
                            '表单方法': form['method'],
                            '输入类型': inp['type'],
                            '输入名称': inp['name'],
                            '输入值': inp['value'],
                            '占位符': inp['placeholder']
                        })
                if form_list:
                    form_df = pd.DataFrame(form_list)
                    form_df.to_excel(writer, sheet_name='表单数据', index=False)
            
            # 页面链接
            if results['page_structure']['links']:
                links_df = pd.DataFrame(results['page_structure']['links'])
                links_df.to_excel(writer, sheet_name='页面链接', index=False)
            
            # 页面图片
            if results['page_structure']['images']:
                images_df = pd.DataFrame(results['page_structure']['images'])
                images_df.to_excel(writer, sheet_name='页面图片', index=False)
        
        print(f"Excel报告已保存到: {filename}")

def main():
    """主函数"""
    url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
    
    print("=== K3网站开发者工具数据分析 ===\n")
    
    analyzer = DevToolsAnalyzer()
    
    # 1. 分析网络请求
    print("1. 分析网络请求...")
    response = analyzer.analyze_network_requests(url)
    
    # 2. 提取JavaScript数据
    print("\n2. 提取JavaScript数据...")
    js_data = analyzer.extract_javascript_data(response)
    
    # 3. 提取API端点
    print("\n3. 提取API端点...")
    api_endpoints = analyzer.extract_api_endpoints(response)
    print(f"找到 {len(api_endpoints)} 个API端点:")
    for endpoint in api_endpoints:
        print(f"  - {endpoint}")
    
    # 4. 提取表单数据
    print("\n4. 提取表单数据...")
    form_data = analyzer.extract_form_data(response)
    print(f"找到 {len(form_data)} 个表单")
    
    # 5. 提取AJAX请求
    print("\n5. 提取AJAX请求...")
    ajax_requests = analyzer.extract_ajax_requests(response)
    print(f"找到 {len(ajax_requests)} 个AJAX请求")
    
    # 6. 分析页面结构
    print("\n6. 分析页面结构...")
    structure = analyzer.analyze_page_structure(response)
    print(f"页面标题: {structure['title']}")
    print(f"Meta标签数量: {len(structure['meta_tags'])}")
    print(f"链接数量: {len(structure['links'])}")
    print(f"图片数量: {len(structure['images'])}")
    
    # 7. 保存分析结果
    print("\n7. 保存分析结果...")
    result_file = analyzer.save_analysis_results(
        url, response, js_data, api_endpoints, 
        form_data, ajax_requests, structure
    )
    
    print(f"\n=== 分析完成 ===")
    print("生成的文件:")
    print(f"- {result_file} (JSON格式的详细分析)")
    print(f"- dev_tools_report_*.xlsx (Excel格式的报告)")

if __name__ == "__main__":
    main()
