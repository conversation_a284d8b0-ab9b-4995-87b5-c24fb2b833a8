import requests
from bs4 import BeautifulSoup
import time
import re
from urllib.parse import urljoin, urlparse

class FrontendCodeExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.k3.cn/',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(self.headers)
        self.base_url = 'https://www.k3.cn'
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问主页面: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            print(f"页面访问成功，状态码: {response.status_code}")
            print(f"页面大小: {len(response.content)} 字节")
            return response
        except Exception as e:
            print(f"页面访问失败: {e}")
            return None
    
    def get_external_resource(self, url):
        """获取外部资源内容"""
        try:
            print(f"  正在获取外部资源: {url}")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"  获取外部资源失败 {url}: {e}")
            return f"/* 获取失败: {e} */"
    
    def extract_all_frontend_code(self, url):
        """提取所有前端代码"""
        response = self.get_page_content(url)
        if not response:
            return None
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        frontend_code = {
            'url': url,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'response_info': {
                'status_code': response.status_code,
                'content_length': len(response.content),
                'content_type': response.headers.get('content-type', '')
            },
            'html_code': {
                'complete_html': response.text,
                'doctype': '',
                'html_tag': '',
                'head_section': '',
                'body_section': ''
            },
            'css_code': {
                'inline_styles': [],
                'external_stylesheets': [],
                'style_attributes': []
            },
            'javascript_code': {
                'inline_scripts': [],
                'external_scripts': [],
                'event_handlers': []
            }
        }
        
        # 1. 提取完整HTML代码
        print("提取HTML代码...")
        frontend_code['html_code']['complete_html'] = response.text
        
        # 提取DOCTYPE
        if soup.contents and hasattr(soup.contents[0], 'strip'):
            frontend_code['html_code']['doctype'] = str(soup.contents[0]).strip()
        
        # 提取HTML标签
        if soup.html:
            frontend_code['html_code']['html_tag'] = str(soup.html)[:200] + '...'
        
        # 提取HEAD部分
        if soup.head:
            frontend_code['html_code']['head_section'] = str(soup.head)
        
        # 提取BODY部分
        if soup.body:
            frontend_code['html_code']['body_section'] = str(soup.body)
        
        # 2. 提取所有CSS代码
        print("提取CSS代码...")
        
        # 内联样式 (style标签)
        for i, style_tag in enumerate(soup.find_all('style'), 1):
            if style_tag.string:
                frontend_code['css_code']['inline_styles'].append({
                    'index': i,
                    'type': style_tag.get('type', 'text/css'),
                    'media': style_tag.get('media', 'all'),
                    'content': style_tag.string,
                    'length': len(style_tag.string),
                    'attributes': dict(style_tag.attrs)
                })
        
        # 外部样式表 (link标签)
        for i, link_tag in enumerate(soup.find_all('link', rel='stylesheet'), 1):
            href = link_tag.get('href', '')
            if href:
                if href.startswith('/'):
                    full_url = urljoin(self.base_url, href)
                elif not href.startswith('http'):
                    full_url = urljoin(url, href)
                else:
                    full_url = href
                
                # 尝试获取外部CSS内容
                css_content = self.get_external_resource(full_url)
                
                frontend_code['css_code']['external_stylesheets'].append({
                    'index': i,
                    'href': href,
                    'full_url': full_url,
                    'media': link_tag.get('media', 'all'),
                    'type': link_tag.get('type', 'text/css'),
                    'content': css_content,
                    'length': len(css_content),
                    'attributes': dict(link_tag.attrs)
                })
        
        # style属性中的内联样式
        for i, element in enumerate(soup.find_all(style=True), 1):
            style_content = element.get('style', '')
            if style_content:
                frontend_code['css_code']['style_attributes'].append({
                    'index': i,
                    'tag': element.name,
                    'class': element.get('class', []),
                    'id': element.get('id', ''),
                    'style_content': style_content,
                    'element_text': element.get_text(strip=True)[:100]
                })
        
        # 3. 提取所有JavaScript代码
        print("提取JavaScript代码...")
        
        # 内联脚本 (script标签)
        for i, script_tag in enumerate(soup.find_all('script'), 1):
            if script_tag.string:
                frontend_code['javascript_code']['inline_scripts'].append({
                    'index': i,
                    'type': script_tag.get('type', 'text/javascript'),
                    'content': script_tag.string,
                    'length': len(script_tag.string),
                    'attributes': dict(script_tag.attrs)
                })
        
        # 外部脚本 (script标签带src)
        for i, script_tag in enumerate(soup.find_all('script', src=True), 1):
            src = script_tag.get('src', '')
            if src:
                if src.startswith('/'):
                    full_url = urljoin(self.base_url, src)
                elif not src.startswith('http'):
                    full_url = urljoin(url, src)
                else:
                    full_url = src
                
                # 尝试获取外部JavaScript内容
                js_content = self.get_external_resource(full_url)
                
                frontend_code['javascript_code']['external_scripts'].append({
                    'index': i,
                    'src': src,
                    'full_url': full_url,
                    'type': script_tag.get('type', 'text/javascript'),
                    'async': script_tag.has_attr('async'),
                    'defer': script_tag.has_attr('defer'),
                    'content': js_content,
                    'length': len(js_content),
                    'attributes': dict(script_tag.attrs)
                })
        
        # 事件处理器 (onclick, onload等)
        event_attributes = ['onclick', 'onload', 'onmouseover', 'onmouseout', 'onchange', 'onsubmit', 'onfocus', 'onblur']
        for i, element in enumerate(soup.find_all(), 1):
            for attr in event_attributes:
                if element.has_attr(attr):
                    frontend_code['javascript_code']['event_handlers'].append({
                        'index': i,
                        'tag': element.name,
                        'event': attr,
                        'handler': element.get(attr),
                        'class': element.get('class', []),
                        'id': element.get('id', ''),
                        'element_text': element.get_text(strip=True)[:50]
                    })
        
        return frontend_code
    
    def save_frontend_code_to_txt(self, frontend_code, filename):
        """保存前端代码到txt文件"""
        if not frontend_code:
            print("没有前端代码可保存")
            return False
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=" * 100 + "\n")
                f.write("K3网站前端代码完整提取\n")
                f.write("=" * 100 + "\n\n")
                
                # 基本信息
                f.write(f"URL: {frontend_code['url']}\n")
                f.write(f"提取时间: {frontend_code['timestamp']}\n")
                f.write(f"HTTP状态码: {frontend_code['response_info']['status_code']}\n")
                f.write(f"内容类型: {frontend_code['response_info']['content_type']}\n")
                f.write(f"页面大小: {frontend_code['response_info']['content_length']} 字节\n\n")
                
                # ==================== HTML代码部分 ====================
                f.write("=" * 80 + " HTML代码 " + "=" * 80 + "\n\n")
                
                f.write("DOCTYPE声明:\n")
                f.write("-" * 50 + "\n")
                f.write(f"{frontend_code['html_code']['doctype']}\n\n")
                
                f.write("完整HTML源代码:\n")
                f.write("-" * 50 + "\n")
                f.write(frontend_code['html_code']['complete_html'])
                f.write("\n\n")
                
                # ==================== CSS代码部分 ====================
                f.write("=" * 80 + " CSS代码 " + "=" * 80 + "\n\n")
                
                # 内联CSS
                f.write(f"内联CSS样式 (共 {len(frontend_code['css_code']['inline_styles'])} 个):\n")
                f.write("-" * 50 + "\n")
                for style in frontend_code['css_code']['inline_styles']:
                    f.write(f"内联CSS #{style['index']} (类型: {style['type']}, 媒体: {style['media']}, 长度: {style['length']}):\n")
                    f.write(f"属性: {style['attributes']}\n")
                    f.write("代码内容:\n")
                    f.write(style['content'])
                    f.write("\n" + "-" * 50 + "\n\n")
                
                # 外部CSS
                f.write(f"外部CSS样式表 (共 {len(frontend_code['css_code']['external_stylesheets'])} 个):\n")
                f.write("-" * 50 + "\n")
                for stylesheet in frontend_code['css_code']['external_stylesheets']:
                    f.write(f"外部CSS #{stylesheet['index']}:\n")
                    f.write(f"原始链接: {stylesheet['href']}\n")
                    f.write(f"完整URL: {stylesheet['full_url']}\n")
                    f.write(f"媒体类型: {stylesheet['media']}\n")
                    f.write(f"内容长度: {stylesheet['length']} 字符\n")
                    f.write(f"属性: {stylesheet['attributes']}\n")
                    f.write("CSS代码内容:\n")
                    f.write(stylesheet['content'])
                    f.write("\n" + "-" * 50 + "\n\n")
                
                # style属性
                f.write(f"元素内联样式 (共 {len(frontend_code['css_code']['style_attributes'])} 个):\n")
                f.write("-" * 50 + "\n")
                for style_attr in frontend_code['css_code']['style_attributes']:
                    f.write(f"内联样式 #{style_attr['index']}:\n")
                    f.write(f"标签: <{style_attr['tag']}>\n")
                    f.write(f"类: {style_attr['class']}\n")
                    f.write(f"ID: {style_attr['id']}\n")
                    f.write(f"元素文本: {style_attr['element_text']}\n")
                    f.write(f"样式内容: {style_attr['style_content']}\n")
                    f.write("-" * 30 + "\n")
                
                # ==================== JavaScript代码部分 ====================
                f.write("\n" + "=" * 80 + " JavaScript代码 " + "=" * 80 + "\n\n")
                
                # 内联JavaScript
                f.write(f"内联JavaScript (共 {len(frontend_code['javascript_code']['inline_scripts'])} 个):\n")
                f.write("-" * 50 + "\n")
                for script in frontend_code['javascript_code']['inline_scripts']:
                    f.write(f"内联JS #{script['index']} (类型: {script['type']}, 长度: {script['length']}):\n")
                    f.write(f"属性: {script['attributes']}\n")
                    f.write("JavaScript代码内容:\n")
                    f.write(script['content'])
                    f.write("\n" + "-" * 50 + "\n\n")
                
                # 外部JavaScript
                f.write(f"外部JavaScript文件 (共 {len(frontend_code['javascript_code']['external_scripts'])} 个):\n")
                f.write("-" * 50 + "\n")
                for script in frontend_code['javascript_code']['external_scripts']:
                    f.write(f"外部JS #{script['index']}:\n")
                    f.write(f"原始链接: {script['src']}\n")
                    f.write(f"完整URL: {script['full_url']}\n")
                    f.write(f"类型: {script['type']}\n")
                    f.write(f"异步加载: {script['async']}\n")
                    f.write(f"延迟加载: {script['defer']}\n")
                    f.write(f"内容长度: {script['length']} 字符\n")
                    f.write(f"属性: {script['attributes']}\n")
                    f.write("JavaScript代码内容:\n")
                    f.write(script['content'])
                    f.write("\n" + "-" * 50 + "\n\n")
                
                # 事件处理器
                f.write(f"事件处理器 (共 {len(frontend_code['javascript_code']['event_handlers'])} 个):\n")
                f.write("-" * 50 + "\n")
                for handler in frontend_code['javascript_code']['event_handlers']:
                    f.write(f"事件处理器 #{handler['index']}:\n")
                    f.write(f"标签: <{handler['tag']}>\n")
                    f.write(f"事件: {handler['event']}\n")
                    f.write(f"处理器代码: {handler['handler']}\n")
                    f.write(f"类: {handler['class']}\n")
                    f.write(f"ID: {handler['id']}\n")
                    f.write(f"元素文本: {handler['element_text']}\n")
                    f.write("-" * 30 + "\n")
                
                # 统计信息
                f.write("\n" + "=" * 80 + " 代码统计 " + "=" * 80 + "\n")
                f.write(f"HTML代码长度: {len(frontend_code['html_code']['complete_html'])} 字符\n")
                f.write(f"内联CSS数量: {len(frontend_code['css_code']['inline_styles'])} 个\n")
                f.write(f"外部CSS数量: {len(frontend_code['css_code']['external_stylesheets'])} 个\n")
                f.write(f"元素内联样式数量: {len(frontend_code['css_code']['style_attributes'])} 个\n")
                f.write(f"内联JavaScript数量: {len(frontend_code['javascript_code']['inline_scripts'])} 个\n")
                f.write(f"外部JavaScript数量: {len(frontend_code['javascript_code']['external_scripts'])} 个\n")
                f.write(f"事件处理器数量: {len(frontend_code['javascript_code']['event_handlers'])} 个\n")
                
                # 计算总代码量
                total_css_length = sum(style['length'] for style in frontend_code['css_code']['inline_styles'])
                total_css_length += sum(sheet['length'] for sheet in frontend_code['css_code']['external_stylesheets'])
                
                total_js_length = sum(script['length'] for script in frontend_code['javascript_code']['inline_scripts'])
                total_js_length += sum(script['length'] for script in frontend_code['javascript_code']['external_scripts'])
                
                f.write(f"CSS代码总长度: {total_css_length} 字符\n")
                f.write(f"JavaScript代码总长度: {total_js_length} 字符\n")
                f.write(f"前端代码总长度: {len(frontend_code['html_code']['complete_html']) + total_css_length + total_js_length} 字符\n")
            
            print(f"\n✅ 前端代码已保存到: {filename}")
            print(f"📊 代码统计:")
            print(f"   - HTML代码: {len(frontend_code['html_code']['complete_html'])} 字符")
            print(f"   - 内联CSS: {len(frontend_code['css_code']['inline_styles'])} 个")
            print(f"   - 外部CSS: {len(frontend_code['css_code']['external_stylesheets'])} 个")
            print(f"   - 内联JS: {len(frontend_code['javascript_code']['inline_scripts'])} 个")
            print(f"   - 外部JS: {len(frontend_code['javascript_code']['external_scripts'])} 个")
            print(f"   - 事件处理器: {len(frontend_code['javascript_code']['event_handlers'])} 个")
            
            return True
            
        except Exception as e:
            print(f"保存txt文件时出错: {e}")
            return False

def main():
    """主函数"""
    url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
    
    print("=== K3网站前端代码提取器 ===\n")
    print(f"目标URL: {url}")
    print("将提取所有HTML、CSS、JavaScript代码...")
    
    extractor = FrontendCodeExtractor()
    
    # 提取前端代码
    print("\n开始提取前端代码...")
    frontend_code = extractor.extract_all_frontend_code(url)
    
    if frontend_code:
        # 保存到txt文件
        timestamp = int(time.time())
        filename = f'k3_frontend_code_{timestamp}.txt'
        success = extractor.save_frontend_code_to_txt(frontend_code, filename)
        
        if success:
            print(f"\n✅ 前端代码提取完成!")
            print(f"📁 文件已保存: {filename}")
        else:
            print(f"\n❌ 文件保存失败")
    else:
        print(f"\n❌ 前端代码提取失败")

if __name__ == "__main__":
    main()
