import requests
from bs4 import BeautifulSoup
import json
import re
import pandas as pd
import time

def analyze_k3_page_content():
    """深度分析K3页面内容"""
    url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.k3.cn/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        print(f"页面访问成功，状态码: {response.status_code}")
        print(f"页面大小: {len(response.content)} 字节")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 1. 分析页面标题和描述
        print(f"\n=== 页面基本信息 ===")
        title = soup.find('title')
        if title:
            print(f"页面标题: {title.get_text()}")
        
        # 2. 查找所有可能包含数据的元素
        print(f"\n=== 查找数据容器 ===")
        
        # 查找可能的产品容器
        product_containers = []
        selectors = [
            '[class*="product"]', '[class*="item"]', '[class*="goods"]',
            '[class*="list"]', '[class*="card"]', '[class*="box"]',
            '[id*="product"]', '[id*="item"]', '[id*="goods"]'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                print(f"选择器 '{selector}': 找到 {len(elements)} 个元素")
                product_containers.extend(elements)
        
        # 3. 分析JavaScript变量
        print(f"\n=== JavaScript变量分析 ===")
        scripts = soup.find_all('script')
        js_variables = {}
        
        for i, script in enumerate(scripts):
            if script.string:
                content = script.string
                
                # 查找变量定义
                var_patterns = [
                    r'var\s+(\w+)\s*=\s*([^;]+);',
                    r'window\.(\w+)\s*=\s*([^;]+);',
                    r'let\s+(\w+)\s*=\s*([^;]+);',
                    r'const\s+(\w+)\s*=\s*([^;]+);'
                ]
                
                for pattern in var_patterns:
                    matches = re.findall(pattern, content)
                    for var_name, var_value in matches:
                        if any(keyword in var_name.lower() for keyword in ['data', 'config', 'list', 'product', 'search']):
                            js_variables[var_name] = var_value.strip()
                            print(f"  变量 {var_name}: {var_value[:100]}...")
        
        # 4. 查找AJAX/API调用
        print(f"\n=== AJAX/API调用分析 ===")
        ajax_calls = []
        
        for script in scripts:
            if script.string:
                content = script.string
                
                # 查找AJAX调用
                ajax_patterns = [
                    r'\.ajax\s*\(\s*\{[^}]*url\s*:\s*["\']([^"\']+)["\']',
                    r'fetch\s*\(\s*["\']([^"\']+)["\']',
                    r'XMLHttpRequest.*?open\s*\([^)]*["\']([^"\']+)["\']',
                    r'["\']https?://[^"\']*(?:api|search|data)[^"\']*["\']'
                ]
                
                for pattern in ajax_patterns:
                    matches = re.findall(pattern, content)
                    ajax_calls.extend(matches)
        
        unique_calls = list(set(ajax_calls))
        for call in unique_calls:
            print(f"  发现API调用: {call}")
        
        # 5. 分析页面中的隐藏数据
        print(f"\n=== 隐藏数据分析 ===")
        
        # 查找隐藏的input字段
        hidden_inputs = soup.find_all('input', type='hidden')
        for inp in hidden_inputs:
            name = inp.get('name', '')
            value = inp.get('value', '')
            if name and value:
                print(f"  隐藏字段 {name}: {value}")
        
        # 查找data属性
        data_elements = []
        for element in soup.find_all():
            if hasattr(element, 'attrs') and element.attrs:
                data_attrs = {k: v for k, v in element.attrs.items() if k.startswith('data-')}
                if data_attrs:
                    data_elements.append({
                        'tag': element.name,
                        'class': element.get('class', []),
                        'data_attrs': data_attrs
                    })
        
        print(f"  找到 {len(data_elements)} 个带data属性的元素")
        for elem in data_elements[:5]:  # 只显示前5个
            print(f"    {elem['tag']}: {elem['data_attrs']}")
        
        # 6. 分析页面结构
        print(f"\n=== 页面结构分析 ===")
        
        # 查找主要内容区域
        main_content = soup.find('main') or soup.find('div', class_=re.compile(r'main|content|container'))
        if main_content:
            print(f"  主内容区域: {main_content.name} (class: {main_content.get('class', [])})")
        
        # 查找导航
        nav = soup.find('nav') or soup.find('div', class_=re.compile(r'nav|menu'))
        if nav:
            print(f"  导航区域: {nav.name} (class: {nav.get('class', [])})")
        
        # 7. 提取所有链接并分析
        print(f"\n=== 链接分析 ===")
        links = soup.find_all('a', href=True)
        
        internal_links = []
        external_links = []
        api_links = []
        
        for link in links:
            href = link['href']
            text = link.get_text(strip=True)
            
            if href.startswith('http'):
                if 'k3.cn' in href:
                    internal_links.append({'href': href, 'text': text})
                else:
                    external_links.append({'href': href, 'text': text})
            elif href.startswith('/'):
                internal_links.append({'href': f"https://www.k3.cn{href}", 'text': text})
            
            # 查找可能的API链接
            if any(keyword in href.lower() for keyword in ['api', 'ajax', 'data', 'search']):
                api_links.append({'href': href, 'text': text})
        
        print(f"  内部链接: {len(internal_links)} 个")
        print(f"  外部链接: {len(external_links)} 个")
        print(f"  可能的API链接: {len(api_links)} 个")
        
        for api_link in api_links[:5]:
            print(f"    API链接: {api_link['href']}")
        
        # 8. 保存详细分析结果
        analysis_result = {
            'url': url,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'page_info': {
                'title': title.get_text() if title else '',
                'size': len(response.content),
                'status_code': response.status_code
            },
            'javascript_variables': js_variables,
            'ajax_calls': unique_calls,
            'data_elements': data_elements,
            'links': {
                'internal': internal_links,
                'external': external_links,
                'api': api_links
            },
            'hidden_inputs': [{'name': inp.get('name'), 'value': inp.get('value')} for inp in hidden_inputs]
        }
        
        # 保存为JSON
        filename = f'k3_deep_analysis_{int(time.time())}.json'
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n=== 分析完成 ===")
        print(f"详细分析结果已保存到: {filename}")
        
        # 创建Excel报告
        create_excel_report(analysis_result)
        
        return analysis_result
        
    except Exception as e:
        print(f"分析失败: {e}")
        return None

def create_excel_report(analysis_result):
    """创建Excel报告"""
    filename = f'k3_deep_report_{int(time.time())}.xlsx'
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 基本信息
        basic_info = pd.DataFrame([{
            'URL': analysis_result['url'],
            '分析时间': analysis_result['timestamp'],
            '页面标题': analysis_result['page_info']['title'],
            '页面大小': analysis_result['page_info']['size'],
            'HTTP状态码': analysis_result['page_info']['status_code']
        }])
        basic_info.to_excel(writer, sheet_name='基本信息', index=False)
        
        # JavaScript变量
        if analysis_result['javascript_variables']:
            js_vars = pd.DataFrame([
                {'变量名': k, '变量值': v} 
                for k, v in analysis_result['javascript_variables'].items()
            ])
            js_vars.to_excel(writer, sheet_name='JavaScript变量', index=False)
        
        # AJAX调用
        if analysis_result['ajax_calls']:
            ajax_df = pd.DataFrame([{'AJAX调用': call} for call in analysis_result['ajax_calls']])
            ajax_df.to_excel(writer, sheet_name='AJAX调用', index=False)
        
        # 内部链接
        if analysis_result['links']['internal']:
            internal_df = pd.DataFrame(analysis_result['links']['internal'])
            internal_df.to_excel(writer, sheet_name='内部链接', index=False)
        
        # API链接
        if analysis_result['links']['api']:
            api_df = pd.DataFrame(analysis_result['links']['api'])
            api_df.to_excel(writer, sheet_name='API链接', index=False)
        
        # Data属性
        if analysis_result['data_elements']:
            data_list = []
            for elem in analysis_result['data_elements']:
                for attr_name, attr_value in elem['data_attrs'].items():
                    data_list.append({
                        '标签': elem['tag'],
                        'Class': ' '.join(elem['class']) if elem['class'] else '',
                        'Data属性': attr_name,
                        '属性值': attr_value
                    })
            if data_list:
                data_df = pd.DataFrame(data_list)
                data_df.to_excel(writer, sheet_name='Data属性', index=False)
    
    print(f"Excel报告已保存到: {filename}")

if __name__ == "__main__":
    analyze_k3_page_content()
