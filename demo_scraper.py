import pandas as pd
import requests
from bs4 import BeautifulSoup

def create_sample_data():
    """创建示例数据来演示Excel导出功能"""
    sample_products = [
        {
            '标题': 'Nike Air Max 270 运动鞋',
            '价格': '¥899',
            '链接': 'https://example.com/nike-air-max-270',
            '图片': 'https://example.com/images/nike-air-max-270.jpg',
            '描述': '舒适透气的运动鞋，适合日常运动和休闲穿着'
        },
        {
            '标题': 'Adidas Ultra Boost 22 跑步鞋',
            '价格': '¥1299',
            '链接': 'https://example.com/adidas-ultra-boost-22',
            '图片': 'https://example.com/images/adidas-ultra-boost-22.jpg',
            '描述': '专业跑步鞋，提供卓越的缓震和回弹性能'
        },
        {
            '标题': 'New Balance 990v5 复古跑鞋',
            '价格': '¥1599',
            '链接': 'https://example.com/new-balance-990v5',
            '图片': 'https://example.com/images/new-balance-990v5.jpg',
            '描述': '经典复古设计，优质材料制作，舒适耐穿'
        },
        {
            '标题': 'Puma RS-X 老爹鞋',
            '价格': '¥699',
            '链接': 'https://example.com/puma-rs-x',
            '图片': 'https://example.com/images/puma-rs-x.jpg',
            '描述': '时尚老爹鞋设计，厚底增高，街头潮流必备'
        },
        {
            '标题': 'Converse Chuck Taylor All Star 帆布鞋',
            '价格': '¥399',
            '链接': 'https://example.com/converse-chuck-taylor',
            '图片': 'https://example.com/images/converse-chuck-taylor.jpg',
            '描述': '经典帆布鞋，百搭款式，青春活力的象征'
        }
    ]
    return sample_products

def save_to_excel(products, filename='运动鞋数据_示例.xlsx'):
    """将产品数据保存到Excel文件"""
    if not products:
        print("没有数据可保存")
        return False
    
    try:
        df = pd.DataFrame(products)
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"数据已保存到 {filename}")
        print(f"共保存了 {len(products)} 条记录")
        return True
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")
        return False

def try_simple_web_scraping():
    """尝试简单的网页抓取示例"""
    print("尝试抓取一个简单的网页作为示例...")
    
    try:
        # 使用一个简单的测试网站
        url = "https://httpbin.org/html"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 提取页面标题和一些文本
        title = soup.find('title')
        h1_tags = soup.find_all('h1')
        p_tags = soup.find_all('p')
        
        scraped_data = []
        
        if title:
            scraped_data.append({
                '标题': title.get_text(strip=True),
                '价格': '示例价格',
                '链接': url,
                '图片': '无图片',
                '描述': '这是一个网页抓取测试示例'
            })
        
        for i, h1 in enumerate(h1_tags):
            scraped_data.append({
                '标题': h1.get_text(strip=True),
                '价格': f'示例价格 {i+1}',
                '链接': url,
                '图片': '无图片',
                '描述': '从H1标签提取的内容'
            })
        
        for i, p in enumerate(p_tags[:3]):  # 只取前3个段落
            text = p.get_text(strip=True)
            if text and len(text) > 10:
                scraped_data.append({
                    '标题': text[:50] + '...' if len(text) > 50 else text,
                    '价格': f'示例价格 {i+1}',
                    '链接': url,
                    '图片': '无图片',
                    '描述': '从段落标签提取的内容'
                })
        
        return scraped_data
        
    except Exception as e:
        print(f"网页抓取失败: {e}")
        return []

def main():
    print("=== 运动鞋数据抓取演示程序 ===\n")
    
    # 1. 创建示例数据
    print("1. 创建示例运动鞋数据...")
    sample_products = create_sample_data()
    
    print(f"创建了 {len(sample_products)} 条示例数据")
    print("\n示例数据预览:")
    for i, product in enumerate(sample_products[:2]):
        print(f"\n第 {i+1} 条:")
        for key, value in product.items():
            print(f"  {key}: {value}")
    
    # 2. 保存示例数据到Excel
    print(f"\n2. 保存示例数据到Excel...")
    save_to_excel(sample_products, '运动鞋数据_示例.xlsx')
    
    # 3. 尝试简单的网页抓取
    print(f"\n3. 尝试简单的网页抓取...")
    scraped_data = try_simple_web_scraping()
    
    if scraped_data:
        print(f"成功抓取了 {len(scraped_data)} 条数据")
        save_to_excel(scraped_data, '网页抓取_示例.xlsx')
    else:
        print("网页抓取未成功")
    
    print(f"\n=== 演示完成 ===")
    print("生成的文件:")
    print("- 运动鞋数据_示例.xlsx (示例数据)")
    if scraped_data:
        print("- 网页抓取_示例.xlsx (抓取的数据)")

if __name__ == "__main__":
    main()
