import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
from urllib.parse import urljoin

class EnhancedLiExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.k3.cn/',
        }
        self.session.headers.update(self.headers)
        self.base_url = 'https://www.k3.cn'
    
    def get_page_with_different_methods(self, url):
        """尝试不同方法获取页面内容"""
        methods = [
            {'name': '直接访问', 'headers': self.headers},
            {'name': '模拟搜索请求', 'headers': {**self.headers, 'Referer': 'https://www.k3.cn/search'}},
            {'name': '模拟AJAX请求', 'headers': {**self.headers, 'X-Requested-With': 'XMLHttpRequest'}},
        ]
        
        for method in methods:
            try:
                print(f"\n尝试方法: {method['name']}")
                response = self.session.get(url, headers=method['headers'], timeout=15)
                print(f"状态码: {response.status_code}")
                print(f"页面大小: {len(response.content)} 字节")
                
                if response.status_code == 200:
                    return response
                    
            except Exception as e:
                print(f"方法 {method['name']} 失败: {e}")
        
        return None
    
    def analyze_page_structure(self, soup):
        """分析页面结构，寻找可能包含产品数据的区域"""
        print("\n=== 页面结构分析 ===")
        
        # 查找所有可能的容器
        containers = []
        
        # 1. 查找content相关的容器
        content_selectors = [
            '.content', '[class*="content"]', '#content',
            '.main', '[class*="main"]', '#main',
            '.container', '[class*="container"]',
            '.wrapper', '[class*="wrapper"]',
            '.search-result', '[class*="search"]', '[class*="result"]',
            '.product-list', '[class*="product"]', '[class*="list"]',
            '.goods', '[class*="goods"]', '[class*="item"]'
        ]
        
        for selector in content_selectors:
            elements = soup.select(selector)
            for i, element in enumerate(elements):
                li_elements = element.find_all('li')
                if li_elements:
                    containers.append({
                        'selector': selector,
                        'index': i,
                        'element': element,
                        'li_count': len(li_elements),
                        'classes': element.get('class', []),
                        'id': element.get('id', ''),
                        'text_length': len(element.get_text(strip=True))
                    })
        
        # 按li数量排序，优先处理包含更多li的容器
        containers.sort(key=lambda x: x['li_count'], reverse=True)
        
        print(f"找到 {len(containers)} 个包含<li>元素的容器:")
        for container in containers[:10]:  # 只显示前10个
            print(f"  {container['selector']} (第{container['index']+1}个): {container['li_count']} 个<li>, classes: {container['classes']}")
        
        return containers
    
    def extract_detailed_li_data(self, li_element, index, container_info):
        """详细提取li元素数据"""
        data = {
            'li_index': index + 1,
            'container_selector': container_info['selector'],
            'container_classes': ' '.join(container_info['classes']),
            'container_id': container_info['id'],
            'raw_text': li_element.get_text(strip=True),
            'html_content': str(li_element),
            'li_classes': ' '.join(li_element.get('class', [])),
            'li_id': li_element.get('id', ''),
        }
        
        # 提取所有属性
        data['all_attributes'] = dict(li_element.attrs)
        
        # 提取链接信息
        links = li_element.find_all('a', href=True)
        data['links'] = []
        for link in links:
            href = link['href']
            if href.startswith('/'):
                href = urljoin(self.base_url, href)
            
            link_data = {
                'url': href,
                'text': link.get_text(strip=True),
                'title': link.get('title', ''),
                'classes': ' '.join(link.get('class', [])),
                'all_attributes': dict(link.attrs)
            }
            data['links'].append(link_data)
        
        # 提取图片信息
        images = li_element.find_all('img')
        data['images'] = []
        for img in images:
            src = img.get('src', '')
            if src and src.startswith('/'):
                src = urljoin(self.base_url, src)
            
            img_data = {
                'src': src,
                'alt': img.get('alt', ''),
                'title': img.get('title', ''),
                'classes': ' '.join(img.get('class', [])),
                'width': img.get('width', ''),
                'height': img.get('height', ''),
                'all_attributes': dict(img.attrs)
            }
            data['images'].append(img_data)
        
        # 提取所有子元素的文本
        data['child_elements'] = []
        for child in li_element.find_all():
            if child.name and child.get_text(strip=True):
                data['child_elements'].append({
                    'tag': child.name,
                    'text': child.get_text(strip=True),
                    'classes': ' '.join(child.get('class', [])),
                    'attributes': dict(child.attrs)
                })
        
        # 尝试识别产品信息
        self.identify_product_info(li_element, data)
        
        return data
    
    def identify_product_info(self, li_element, data):
        """尝试识别产品相关信息"""
        text = li_element.get_text()
        
        # 查找价格信息
        price_patterns = [
            r'[¥￥$]\s*[\d,]+\.?\d*',  # ¥123.45
            r'[\d,]+\.?\d*\s*[元块]',   # 123元
            r'价格[：:]\s*[\d,]+\.?\d*', # 价格：123
            r'[\d,]+\.?\d*\s*yuan',    # 123yuan
        ]
        
        for pattern in price_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data['detected_price'] = match.group()
                break
        
        # 查找可能的产品名称
        if len(text) > 5 and len(text) < 200:  # 合理的产品名称长度
            # 检查是否包含产品相关关键词
            product_keywords = ['鞋', 'shoe', '靴', 'boot', '拖鞋', 'slipper', '运动', 'sport']
            if any(keyword in text.lower() for keyword in product_keywords):
                data['possible_product_name'] = text
        
        # 查找品牌信息
        brand_keywords = ['nike', 'adidas', 'puma', 'converse', 'vans', 'new balance', 'reebok']
        for brand in brand_keywords:
            if brand in text.lower():
                data['detected_brand'] = brand
                break
        
        # 查找尺码信息
        size_pattern = r'(\d{2,2}\.?\d?)\s*码|size\s*(\d{2,2}\.?\d?)|(\d{2,2}\.?\d?)\s*号'
        size_match = re.search(size_pattern, text, re.IGNORECASE)
        if size_match:
            data['detected_size'] = size_match.group()
    
    def process_url_enhanced(self, url):
        """增强版URL处理"""
        print(f"=== 增强版Li数据提取 ===")
        print(f"目标URL: {url}")
        
        # 获取页面内容
        response = self.get_page_with_different_methods(url)
        if not response:
            print("无法获取页面内容")
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 分析页面结构
        containers = self.analyze_page_structure(soup)
        
        if not containers:
            print("未找到包含<li>元素的容器")
            return []
        
        all_li_data = []
        
        # 处理每个容器
        for container_info in containers:
            print(f"\n=== 处理容器: {container_info['selector']} ===")
            print(f"容器类: {container_info['classes']}")
            print(f"Li元素数量: {container_info['li_count']}")
            
            li_elements = container_info['element'].find_all('li')
            
            for i, li in enumerate(li_elements):
                li_data = self.extract_detailed_li_data(li, i, container_info)
                all_li_data.append(li_data)
                
                # 显示提取结果
                if li_data['raw_text']:
                    preview = li_data['raw_text'][:80] + '...' if len(li_data['raw_text']) > 80 else li_data['raw_text']
                    print(f"  Li {i+1}: {preview}")
                    
                    if li_data.get('detected_price'):
                        print(f"    检测到价格: {li_data['detected_price']}")
                    if li_data.get('possible_product_name'):
                        print(f"    可能的产品名: {li_data['possible_product_name'][:50]}...")
                    if li_data.get('detected_brand'):
                        print(f"    检测到品牌: {li_data['detected_brand']}")
        
        return all_li_data
    
    def save_enhanced_excel(self, li_data_list, filename):
        """保存增强版Excel"""
        if not li_data_list:
            print("没有数据可保存")
            return False
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 主数据表
                main_data = []
                for item in li_data_list:
                    main_row = {
                        'Li索引': item['li_index'],
                        '容器选择器': item['container_selector'],
                        '容器类': item['container_classes'],
                        '容器ID': item['container_id'],
                        'Li类': item['li_classes'],
                        'Li ID': item['li_id'],
                        '文本内容': item['raw_text'],
                        '检测到的价格': item.get('detected_price', ''),
                        '可能的产品名': item.get('possible_product_name', ''),
                        '检测到的品牌': item.get('detected_brand', ''),
                        '检测到的尺码': item.get('detected_size', ''),
                        '链接数量': len(item['links']),
                        '图片数量': len(item['images']),
                        '子元素数量': len(item['child_elements']),
                        '所有属性': str(item['all_attributes'])
                    }
                    main_data.append(main_row)
                
                main_df = pd.DataFrame(main_data)
                main_df.to_excel(writer, sheet_name='Li元素详细数据', index=False)
                
                # 链接详细数据
                links_data = []
                for item in li_data_list:
                    for j, link in enumerate(item['links']):
                        links_data.append({
                            'Li索引': item['li_index'],
                            '链接索引': j + 1,
                            'URL': link['url'],
                            '链接文本': link['text'],
                            '链接标题': link['title'],
                            '链接类': link['classes'],
                            '链接属性': str(link['all_attributes'])
                        })
                
                if links_data:
                    links_df = pd.DataFrame(links_data)
                    links_df.to_excel(writer, sheet_name='链接详细数据', index=False)
                
                # 图片详细数据
                images_data = []
                for item in li_data_list:
                    for j, img in enumerate(item['images']):
                        images_data.append({
                            'Li索引': item['li_index'],
                            '图片索引': j + 1,
                            '图片URL': img['src'],
                            '图片Alt': img['alt'],
                            '图片标题': img['title'],
                            '图片类': img['classes'],
                            '宽度': img['width'],
                            '高度': img['height'],
                            '图片属性': str(img['all_attributes'])
                        })
                
                if images_data:
                    images_df = pd.DataFrame(images_data)
                    images_df.to_excel(writer, sheet_name='图片详细数据', index=False)
                
                # 产品信息汇总
                product_data = []
                for item in li_data_list:
                    if (item.get('detected_price') or 
                        item.get('possible_product_name') or 
                        item.get('detected_brand')):
                        product_data.append({
                            'Li索引': item['li_index'],
                            '产品名称': item.get('possible_product_name', ''),
                            '价格': item.get('detected_price', ''),
                            '品牌': item.get('detected_brand', ''),
                            '尺码': item.get('detected_size', ''),
                            '完整文本': item['raw_text']
                        })
                
                if product_data:
                    product_df = pd.DataFrame(product_data)
                    product_df.to_excel(writer, sheet_name='疑似产品信息', index=False)
            
            print(f"\n增强版数据已保存到: {filename}")
            print(f"总Li元素: {len(li_data_list)}")
            print(f"总链接: {len(links_data)}")
            print(f"总图片: {len(images_data)}")
            print(f"疑似产品: {len(product_data)}")
            
            return True
            
        except Exception as e:
            print(f"保存Excel失败: {e}")
            return False

def main():
    """主函数"""
    url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
    
    extractor = EnhancedLiExtractor()
    
    # 提取数据
    li_data = extractor.process_url_enhanced(url)
    
    if li_data:
        # 保存数据
        timestamp = int(time.time())
        filename = f'k3_enhanced_li_data_{timestamp}.xlsx'
        success = extractor.save_enhanced_excel(li_data, filename)
        
        if success:
            print(f"\n✅ 增强版数据提取完成!")
        else:
            print(f"\n❌ 数据保存失败")
    else:
        print(f"\n❌ 未提取到数据")

if __name__ == "__main__":
    main()
