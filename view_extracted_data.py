import pandas as pd
import os
import json

def view_latest_excel_data():
    """查看最新生成的Excel数据"""
    # 查找最新的Excel文件
    excel_files = [f for f in os.listdir('.') if f.startswith('k3_content_li_data_') and f.endswith('.xlsx')]
    
    if not excel_files:
        print("没有找到数据文件")
        return
    
    # 使用最新的文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"=== 查看文件: {latest_file} ===\n")
    
    try:
        # 读取所有工作表
        excel_data = pd.read_excel(latest_file, sheet_name=None)
        
        print("📋 文件包含的工作表:")
        for sheet_name in excel_data.keys():
            print(f"  - {sheet_name}")
        
        # 1. 显示Li元素详细数据
        if 'Li元素详细数据' in excel_data:
            print(f"\n=== Li元素详细数据 ===")
            li_data = excel_data['Li元素详细数据']
            print(f"总共 {len(li_data)} 个Li元素\n")
            
            for i, row in li_data.iterrows():
                print(f"Li {row['Li索引']}:")
                print(f"  📝 文本内容: {row['完整文本内容']}")
                print(f"  🎨 CSS类: {row['Li的CSS类']}")
                print(f"  🔗 链接数量: {row['包含链接数量']}")
                print(f"  🖼️ 图片数量: {row['包含图片数量']}")
                
                if row['第一个链接URL']:
                    print(f"  🔗 第一个链接: {row['第一个链接URL']}")
                    print(f"     链接文本: {row['第一个链接文本']}")
                
                if row['第一个图片URL']:
                    print(f"  🖼️ 第一个图片: {row['第一个图片URL']}")
                    print(f"     图片Alt: {row['第一个图片Alt']}")
                
                if row['产品标题']:
                    print(f"  🏷️ 产品标题: {row['产品标题']}")
                if row['产品价格']:
                    print(f"  💰 产品价格: {row['产品价格']}")
                
                print()
        
        # 2. 显示链接详细数据
        if '所有链接详细数据' in excel_data:
            print(f"\n=== 所有链接详细数据 ===")
            links_data = excel_data['所有链接详细数据']
            print(f"总共 {len(links_data)} 个链接\n")
            
            # 按链接类型分组显示
            link_types = links_data['链接类型判断'].value_counts()
            print("🔗 链接类型统计:")
            for link_type, count in link_types.items():
                print(f"  - {link_type}: {count} 个")
            
            print(f"\n🔗 链接详情 (前10个):")
            for i, row in links_data.head(10).iterrows():
                print(f"  {row['链接序号']}. [{row['链接类型判断']}] {row['链接显示文本']}")
                print(f"     URL: {row['链接URL']}")
                print(f"     所属Li: {row['所属Li文本'][:50]}...")
                print()
        
        # 3. 显示图片详细数据
        if '所有图片详细数据' in excel_data:
            print(f"\n=== 所有图片详细数据 ===")
            images_data = excel_data['所有图片详细数据']
            print(f"总共 {len(images_data)} 张图片\n")
            
            for i, row in images_data.iterrows():
                print(f"图片 {row['图片序号']}:")
                print(f"  🖼️ URL: {row['图片URL']}")
                print(f"  📝 Alt文本: {row['图片Alt文本']}")
                print(f"  🏷️ 类型: {row['图片类型判断']}")
                print(f"  📍 所属Li: {row['所属Li文本'][:50]}...")
                print()
        
        # 4. 显示子元素数据
        if '所有子元素数据' in excel_data:
            print(f"\n=== 所有子元素数据 ===")
            elements_data = excel_data['所有子元素数据']
            print(f"总共 {len(elements_data)} 个子元素\n")
            
            # 按元素标签分组统计
            element_tags = elements_data['元素标签'].value_counts()
            print("🏷️ 元素标签统计:")
            for tag, count in element_tags.head(10).items():
                print(f"  - <{tag}>: {count} 个")
            
            print(f"\n📝 有意义的子元素 (前10个):")
            meaningful_elements = elements_data[elements_data['元素文本'].str.len() > 5]
            for i, row in meaningful_elements.head(10).iterrows():
                print(f"  <{row['元素标签']}> {row['元素文本'][:50]}...")
                if row['元素类']:
                    print(f"    类: {row['元素类']}")
                print()
        
        # 5. 显示统计汇总
        if '数据统计汇总' in excel_data:
            print(f"\n=== 数据统计汇总 ===")
            stats_data = excel_data['数据统计汇总']
            stats = stats_data.iloc[0]
            
            print(f"📊 提取统计:")
            print(f"  - 总Li元素数量: {stats['总Li元素数量']}")
            print(f"  - 总链接数量: {stats['总链接数量']}")
            print(f"  - 总图片数量: {stats['总图片数量']}")
            print(f"  - 总子元素数量: {stats['总子元素数量']}")
            print(f"  - 有链接的Li数量: {stats['有链接的Li数量']}")
            print(f"  - 有图片的Li数量: {stats['有图片的Li数量']}")
            print(f"  - 有产品信息的Li数量: {stats['有产品信息的Li数量']}")
            print(f"  - 提取时间: {stats['提取时间']}")
            print(f"  - 目标网址: {stats['目标网址']}")
        
    except Exception as e:
        print(f"读取Excel文件失败: {e}")

def export_links_to_text():
    """导出所有链接到文本文件"""
    excel_files = [f for f in os.listdir('.') if f.startswith('k3_content_li_data_') and f.endswith('.xlsx')]
    
    if not excel_files:
        print("没有找到数据文件")
        return
    
    latest_file = max(excel_files, key=os.path.getctime)
    
    try:
        links_data = pd.read_excel(latest_file, sheet_name='所有链接详细数据')
        
        # 导出链接到文本文件
        with open('extracted_links.txt', 'w', encoding='utf-8') as f:
            f.write("=== K3网站提取的所有链接 ===\n\n")
            
            for i, row in links_data.iterrows():
                f.write(f"链接 {row['链接序号']} [{row['链接类型判断']}]\n")
                f.write(f"文本: {row['链接显示文本']}\n")
                f.write(f"URL: {row['链接URL']}\n")
                f.write(f"所属Li: {row['所属Li文本']}\n")
                f.write("-" * 50 + "\n")
        
        print(f"✅ 链接已导出到: extracted_links.txt")
        
    except Exception as e:
        print(f"导出链接失败: {e}")

def export_images_to_text():
    """导出所有图片信息到文本文件"""
    excel_files = [f for f in os.listdir('.') if f.startswith('k3_content_li_data_') and f.endswith('.xlsx')]
    
    if not excel_files:
        print("没有找到数据文件")
        return
    
    latest_file = max(excel_files, key=os.path.getctime)
    
    try:
        images_data = pd.read_excel(latest_file, sheet_name='所有图片详细数据')
        
        # 导出图片信息到文本文件
        with open('extracted_images.txt', 'w', encoding='utf-8') as f:
            f.write("=== K3网站提取的所有图片 ===\n\n")
            
            for i, row in images_data.iterrows():
                f.write(f"图片 {row['图片序号']} [{row['图片类型判断']}]\n")
                f.write(f"URL: {row['图片URL']}\n")
                f.write(f"Alt文本: {row['图片Alt文本']}\n")
                f.write(f"标题: {row['图片标题属性']}\n")
                f.write(f"所属Li: {row['所属Li文本']}\n")
                f.write("-" * 50 + "\n")
        
        print(f"✅ 图片信息已导出到: extracted_images.txt")
        
    except Exception as e:
        print(f"导出图片信息失败: {e}")

def main():
    """主函数"""
    print("=== K3网站Li数据查看工具 ===\n")
    
    # 1. 查看Excel数据
    view_latest_excel_data()
    
    print("\n" + "="*60)
    
    # 2. 导出链接到文本文件
    print("\n=== 导出数据到文本文件 ===")
    export_links_to_text()
    export_images_to_text()
    
    print(f"\n✅ 数据查看完成!")
    print(f"📁 生成的文件:")
    print(f"  - extracted_links.txt (所有链接)")
    print(f"  - extracted_images.txt (所有图片)")

if __name__ == "__main__":
    main()
