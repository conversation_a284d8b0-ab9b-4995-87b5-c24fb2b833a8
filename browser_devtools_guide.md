# 浏览器开发者工具使用指南 - 获取K3网站数据

## 🎯 目标
通过浏览器开发者工具分析 `https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html` 的网络请求，获取实际的数据接口。

## 📋 步骤详解

### 第一步：打开开发者工具
1. 打开Chrome或Edge浏览器
2. 访问 `https://www.k3.cn`
3. 按 `F12` 键或右键选择"检查"
4. 点击 `Network` (网络) 标签

### 第二步：清空网络日志
1. 在Network标签中，点击 🚫 清空按钮
2. 确保 `Preserve log` (保留日志) 选项已勾选

### 第三步：执行登录操作
1. 在网站上点击登录
2. 输入用户名和密码
3. 观察Network标签中出现的请求

**关键请求类型**：
- 登录请求通常是POST方法
- URL可能包含 `/login`, `/auth`, `/api/login` 等
- 响应可能包含token或session信息

### 第四步：执行搜索操作
1. 登录成功后，进行搜索操作
2. 在搜索框输入"运动鞋"
3. 点击搜索或按回车

### 第五步：分析网络请求

#### 5.1 筛选重要请求
在Network标签中，关注以下类型的请求：
- **XHR** - AJAX请求，通常包含JSON数据
- **Fetch** - 现代的异步请求
- **Doc** - 页面文档请求

#### 5.2 查看请求详情
对于每个重要请求，点击查看：

**Headers标签**：
```
General:
  Request URL: https://www.k3.cn/api/search
  Request Method: POST
  Status Code: 200

Request Headers:
  Accept: application/json
  Content-Type: application/json
  Cookie: session_id=xxx; user_token=yyy
  Referer: https://www.k3.cn/search
  User-Agent: Mozilla/5.0...
```

**Payload标签** (POST请求的数据)：
```json
{
  "keyword": "运动鞋",
  "category": "all",
  "page": 1,
  "pageSize": 20,
  "sortBy": "default"
}
```

**Response标签** (服务器返回的数据)：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 1234,
    "products": [
      {
        "id": "12345",
        "title": "Nike运动鞋",
        "price": "299.00",
        "image": "https://...",
        "url": "https://..."
      }
    ]
  }
}
```

### 第六步：复制请求信息

#### 6.1 复制为cURL
1. 右键点击重要的请求
2. 选择 `Copy` > `Copy as cURL`
3. 保存到文本文件

#### 6.2 复制为Python代码
1. 右键点击请求
2. 选择 `Copy` > `Copy as Python requests`
3. 保存代码

### 第七步：提取关键信息

记录以下信息：

#### 登录相关
- 登录URL
- 登录方法 (POST)
- 登录参数格式
- 返回的token/session信息

#### 搜索相关
- 搜索API URL
- 请求方法 (GET/POST)
- 请求参数
- 必需的Headers (Cookie, Referer等)
- 响应数据格式

## 🔧 实际操作示例

### 示例1：分析登录请求
```
URL: https://www.k3.cn/api/user/login
Method: POST
Headers:
  Content-Type: application/json
  X-Requested-With: XMLHttpRequest

Payload:
{
  "username": "your_username",
  "password": "your_password",
  "captcha": "1234"
}

Response:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "user_id": "12345"
  }
}
```

### 示例2：分析搜索请求
```
URL: https://www.k3.cn/api/search/products
Method: POST
Headers:
  Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
  Content-Type: application/json

Payload:
{
  "keyword": "运动鞋",
  "category": "all",
  "page": 1,
  "limit": 20
}
```

## 🐍 转换为Python代码

基于开发者工具的发现，编写Python代码：

```python
import requests
import json

class K3Scraper:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://www.k3.cn"
        
    def login(self, username, password):
        """登录获取token"""
        login_url = f"{self.base_url}/api/user/login"
        data = {
            "username": username,
            "password": password
        }
        
        response = self.session.post(login_url, json=data)
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                token = result['data']['token']
                self.session.headers.update({
                    'Authorization': f'Bearer {token}'
                })
                return True
        return False
    
    def search_products(self, keyword, page=1):
        """搜索产品"""
        search_url = f"{self.base_url}/api/search/products"
        data = {
            "keyword": keyword,
            "category": "all",
            "page": page,
            "limit": 20
        }
        
        response = self.session.post(search_url, json=data)
        if response.status_code == 200:
            return response.json()
        return None

# 使用示例
scraper = K3Scraper()
if scraper.login("your_username", "your_password"):
    results = scraper.search_products("运动鞋")
    print(json.dumps(results, ensure_ascii=False, indent=2))
```

## 📝 注意事项

1. **Cookie管理**: 确保保持登录状态的Cookie
2. **请求头**: 复制所有必要的请求头
3. **请求频率**: 避免过于频繁的请求
4. **数据格式**: 注意JSON vs Form数据格式
5. **错误处理**: 处理登录失败、请求超时等情况

## 🔍 常见问题排查

### 问题1：请求返回401/403
- 检查是否正确传递了认证信息
- 确认Cookie或Token是否有效
- 验证请求头是否完整

### 问题2：请求返回空数据
- 确认搜索参数格式正确
- 检查是否需要额外的过滤条件
- 验证API URL是否正确

### 问题3：请求被拒绝
- 检查User-Agent是否设置
- 确认Referer头是否正确
- 考虑添加延时避免频率限制

## 🎯 最终目标

通过以上步骤，您应该能够：
1. 找到实际的搜索数据API
2. 获取必要的认证信息
3. 编写Python代码自动获取数据
4. 将数据保存为Excel或其他格式

---

**提示**: 每个网站的结构都不同，需要根据实际观察到的网络请求进行调整。开发者工具是最可靠的数据来源分析方法。
