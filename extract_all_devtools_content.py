import requests
from bs4 import BeautifulSoup
import time
import re
import json
from urllib.parse import urljoin, urlparse

class DevToolsContentExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.k3.cn/',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(self.headers)
        self.base_url = 'https://www.k3.cn'
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            print(f"页面访问成功，状态码: {response.status_code}")
            print(f"页面大小: {len(response.content)} 字节")
            return response
        except Exception as e:
            print(f"页面访问失败: {e}")
            return None
    
    def extract_all_content(self, url):
        """提取页面的所有开发者工具内容"""
        response = self.get_page_content(url)
        if not response:
            return None
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        content_data = {
            'url': url,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'response_info': {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'content_length': len(response.content)
            },
            'html_structure': {},
            'all_elements': [],
            'all_text_content': [],
            'all_links': [],
            'all_images': [],
            'all_scripts': [],
            'all_styles': [],
            'all_forms': [],
            'all_inputs': [],
            'all_meta_tags': [],
            'all_attributes': [],
            'javascript_content': [],
            'css_content': []
        }
        
        # 1. 基本HTML结构
        content_data['html_structure'] = {
            'title': soup.title.string if soup.title else '',
            'doctype': str(soup.contents[0]) if soup.contents and hasattr(soup.contents[0], 'strip') else '',
            'html_tag_attrs': dict(soup.html.attrs) if soup.html else {},
            'head_content_length': len(str(soup.head)) if soup.head else 0,
            'body_content_length': len(str(soup.body)) if soup.body else 0
        }
        
        # 2. 提取所有元素
        print("提取所有HTML元素...")
        for element in soup.find_all():
            if element.name:
                element_data = {
                    'tag': element.name,
                    'text': element.get_text(strip=True)[:200],  # 限制文本长度
                    'attributes': dict(element.attrs),
                    'classes': element.get('class', []),
                    'id': element.get('id', ''),
                    'parent': element.parent.name if element.parent and element.parent.name else '',
                    'children_count': len(element.find_all())
                }
                content_data['all_elements'].append(element_data)
        
        # 3. 提取所有文本内容
        print("提取所有文本内容...")
        text_elements = soup.find_all(text=True)
        for text in text_elements:
            text_content = text.strip()
            if text_content and len(text_content) > 2:
                parent_tag = text.parent.name if text.parent else 'unknown'
                content_data['all_text_content'].append({
                    'text': text_content,
                    'parent_tag': parent_tag,
                    'parent_class': ' '.join(text.parent.get('class', [])) if text.parent else '',
                    'length': len(text_content)
                })
        
        # 4. 提取所有链接
        print("提取所有链接...")
        for link in soup.find_all('a'):
            href = link.get('href', '')
            if href:
                if href.startswith('/'):
                    href = urljoin(self.base_url, href)
                
                link_data = {
                    'url': href,
                    'text': link.get_text(strip=True),
                    'title': link.get('title', ''),
                    'target': link.get('target', ''),
                    'rel': link.get('rel', []),
                    'class': link.get('class', []),
                    'id': link.get('id', ''),
                    'all_attributes': dict(link.attrs)
                }
                content_data['all_links'].append(link_data)
        
        # 5. 提取所有图片
        print("提取所有图片...")
        for img in soup.find_all('img'):
            src = img.get('src', '')
            if src and src.startswith('/'):
                src = urljoin(self.base_url, src)
            
            img_data = {
                'src': src,
                'alt': img.get('alt', ''),
                'title': img.get('title', ''),
                'width': img.get('width', ''),
                'height': img.get('height', ''),
                'class': img.get('class', []),
                'id': img.get('id', ''),
                'loading': img.get('loading', ''),
                'data_src': img.get('data-src', ''),
                'all_attributes': dict(img.attrs)
            }
            content_data['all_images'].append(img_data)
        
        # 6. 提取所有脚本
        print("提取所有JavaScript...")
        for script in soup.find_all('script'):
            script_data = {
                'src': script.get('src', ''),
                'type': script.get('type', ''),
                'content': script.string[:1000] if script.string else '',  # 限制内容长度
                'content_length': len(script.string) if script.string else 0,
                'attributes': dict(script.attrs)
            }
            content_data['all_scripts'].append(script_data)
            
            # 提取JavaScript内容
            if script.string:
                content_data['javascript_content'].append({
                    'content': script.string,
                    'length': len(script.string),
                    'src': script.get('src', 'inline')
                })
        
        # 7. 提取所有样式
        print("提取所有CSS...")
        for style in soup.find_all(['style', 'link']):
            if style.name == 'style':
                style_data = {
                    'type': 'inline',
                    'content': style.string[:1000] if style.string else '',
                    'content_length': len(style.string) if style.string else 0,
                    'attributes': dict(style.attrs)
                }
                content_data['all_styles'].append(style_data)
                
                if style.string:
                    content_data['css_content'].append({
                        'content': style.string,
                        'length': len(style.string),
                        'type': 'inline'
                    })
            
            elif style.name == 'link' and style.get('rel') and 'stylesheet' in style.get('rel'):
                style_data = {
                    'type': 'external',
                    'href': style.get('href', ''),
                    'media': style.get('media', ''),
                    'attributes': dict(style.attrs)
                }
                content_data['all_styles'].append(style_data)
        
        # 8. 提取所有表单
        print("提取所有表单...")
        for form in soup.find_all('form'):
            form_data = {
                'action': form.get('action', ''),
                'method': form.get('method', 'GET'),
                'enctype': form.get('enctype', ''),
                'class': form.get('class', []),
                'id': form.get('id', ''),
                'attributes': dict(form.attrs),
                'inputs_count': len(form.find_all(['input', 'select', 'textarea']))
            }
            content_data['all_forms'].append(form_data)
        
        # 9. 提取所有输入元素
        print("提取所有输入元素...")
        for input_elem in soup.find_all(['input', 'select', 'textarea', 'button']):
            input_data = {
                'tag': input_elem.name,
                'type': input_elem.get('type', ''),
                'name': input_elem.get('name', ''),
                'value': input_elem.get('value', ''),
                'placeholder': input_elem.get('placeholder', ''),
                'class': input_elem.get('class', []),
                'id': input_elem.get('id', ''),
                'required': input_elem.has_attr('required'),
                'disabled': input_elem.has_attr('disabled'),
                'attributes': dict(input_elem.attrs)
            }
            content_data['all_inputs'].append(input_data)
        
        # 10. 提取所有Meta标签
        print("提取所有Meta标签...")
        for meta in soup.find_all('meta'):
            meta_data = {
                'name': meta.get('name', ''),
                'property': meta.get('property', ''),
                'content': meta.get('content', ''),
                'charset': meta.get('charset', ''),
                'http_equiv': meta.get('http-equiv', ''),
                'attributes': dict(meta.attrs)
            }
            content_data['all_meta_tags'].append(meta_data)
        
        # 11. 提取所有data属性
        print("提取所有data属性...")
        for element in soup.find_all():
            if hasattr(element, 'attrs') and element.attrs:
                data_attrs = {k: v for k, v in element.attrs.items() if k.startswith('data-')}
                if data_attrs:
                    content_data['all_attributes'].append({
                        'tag': element.name,
                        'class': element.get('class', []),
                        'id': element.get('id', ''),
                        'data_attributes': data_attrs,
                        'text': element.get_text(strip=True)[:100]
                    })
        
        return content_data
    
    def save_to_txt(self, content_data, filename):
        """保存所有内容到txt文件"""
        if not content_data:
            print("没有数据可保存")
            return False
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("K3网站开发者工具完整内容提取\n")
                f.write("=" * 80 + "\n\n")
                
                # 基本信息
                f.write(f"URL: {content_data['url']}\n")
                f.write(f"提取时间: {content_data['timestamp']}\n")
                f.write(f"HTTP状态码: {content_data['response_info']['status_code']}\n")
                f.write(f"页面大小: {content_data['response_info']['content_length']} 字节\n\n")
                
                # HTML结构
                f.write("=" * 50 + " HTML结构 " + "=" * 50 + "\n")
                f.write(f"页面标题: {content_data['html_structure']['title']}\n")
                f.write(f"HTML标签属性: {content_data['html_structure']['html_tag_attrs']}\n")
                f.write(f"Head内容长度: {content_data['html_structure']['head_content_length']}\n")
                f.write(f"Body内容长度: {content_data['html_structure']['body_content_length']}\n\n")
                
                # 响应头
                f.write("=" * 50 + " HTTP响应头 " + "=" * 50 + "\n")
                for key, value in content_data['response_info']['headers'].items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # Meta标签
                f.write("=" * 50 + " Meta标签 " + "=" * 50 + "\n")
                for i, meta in enumerate(content_data['all_meta_tags'], 1):
                    f.write(f"Meta {i}:\n")
                    for key, value in meta.items():
                        if value:
                            f.write(f"  {key}: {value}\n")
                    f.write("\n")
                
                # 所有链接
                f.write("=" * 50 + " 所有链接 " + "=" * 50 + "\n")
                for i, link in enumerate(content_data['all_links'], 1):
                    f.write(f"链接 {i}:\n")
                    f.write(f"  URL: {link['url']}\n")
                    f.write(f"  文本: {link['text']}\n")
                    f.write(f"  标题: {link['title']}\n")
                    f.write(f"  目标: {link['target']}\n")
                    f.write(f"  类: {link['class']}\n")
                    f.write(f"  ID: {link['id']}\n")
                    f.write(f"  所有属性: {link['all_attributes']}\n")
                    f.write("\n")
                
                # 所有图片
                f.write("=" * 50 + " 所有图片 " + "=" * 50 + "\n")
                for i, img in enumerate(content_data['all_images'], 1):
                    f.write(f"图片 {i}:\n")
                    f.write(f"  URL: {img['src']}\n")
                    f.write(f"  Alt: {img['alt']}\n")
                    f.write(f"  标题: {img['title']}\n")
                    f.write(f"  尺寸: {img['width']} x {img['height']}\n")
                    f.write(f"  类: {img['class']}\n")
                    f.write(f"  ID: {img['id']}\n")
                    f.write(f"  所有属性: {img['all_attributes']}\n")
                    f.write("\n")
                
                # 所有表单
                f.write("=" * 50 + " 所有表单 " + "=" * 50 + "\n")
                for i, form in enumerate(content_data['all_forms'], 1):
                    f.write(f"表单 {i}:\n")
                    f.write(f"  Action: {form['action']}\n")
                    f.write(f"  Method: {form['method']}\n")
                    f.write(f"  Enctype: {form['enctype']}\n")
                    f.write(f"  类: {form['class']}\n")
                    f.write(f"  ID: {form['id']}\n")
                    f.write(f"  输入元素数量: {form['inputs_count']}\n")
                    f.write(f"  所有属性: {form['attributes']}\n")
                    f.write("\n")
                
                # 所有输入元素
                f.write("=" * 50 + " 所有输入元素 " + "=" * 50 + "\n")
                for i, input_elem in enumerate(content_data['all_inputs'], 1):
                    f.write(f"输入元素 {i}:\n")
                    f.write(f"  标签: {input_elem['tag']}\n")
                    f.write(f"  类型: {input_elem['type']}\n")
                    f.write(f"  名称: {input_elem['name']}\n")
                    f.write(f"  值: {input_elem['value']}\n")
                    f.write(f"  占位符: {input_elem['placeholder']}\n")
                    f.write(f"  类: {input_elem['class']}\n")
                    f.write(f"  ID: {input_elem['id']}\n")
                    f.write(f"  必填: {input_elem['required']}\n")
                    f.write(f"  禁用: {input_elem['disabled']}\n")
                    f.write(f"  所有属性: {input_elem['attributes']}\n")
                    f.write("\n")
                
                # JavaScript内容
                f.write("=" * 50 + " JavaScript内容 " + "=" * 50 + "\n")
                for i, js in enumerate(content_data['javascript_content'], 1):
                    f.write(f"JavaScript {i} (来源: {js['src']}, 长度: {js['length']}):\n")
                    f.write(f"{js['content'][:2000]}...\n\n")  # 只显示前2000字符
                
                # CSS内容
                f.write("=" * 50 + " CSS内容 " + "=" * 50 + "\n")
                for i, css in enumerate(content_data['css_content'], 1):
                    f.write(f"CSS {i} (类型: {css['type']}, 长度: {css['length']}):\n")
                    f.write(f"{css['content'][:2000]}...\n\n")  # 只显示前2000字符
                
                # Data属性
                f.write("=" * 50 + " Data属性 " + "=" * 50 + "\n")
                for i, attr in enumerate(content_data['all_attributes'], 1):
                    f.write(f"Data属性 {i}:\n")
                    f.write(f"  标签: {attr['tag']}\n")
                    f.write(f"  类: {attr['class']}\n")
                    f.write(f"  ID: {attr['id']}\n")
                    f.write(f"  文本: {attr['text']}\n")
                    f.write(f"  Data属性: {attr['data_attributes']}\n")
                    f.write("\n")
                
                # 所有文本内容
                f.write("=" * 50 + " 所有文本内容 " + "=" * 50 + "\n")
                for i, text in enumerate(content_data['all_text_content'], 1):
                    if len(text['text']) > 10:  # 只显示有意义的文本
                        f.write(f"文本 {i} (父标签: {text['parent_tag']}, 类: {text['parent_class']}):\n")
                        f.write(f"  {text['text']}\n")
                        f.write("\n")
                
                # 所有HTML元素
                f.write("=" * 50 + " 所有HTML元素 " + "=" * 50 + "\n")
                for i, element in enumerate(content_data['all_elements'], 1):
                    f.write(f"元素 {i}:\n")
                    f.write(f"  标签: {element['tag']}\n")
                    f.write(f"  文本: {element['text']}\n")
                    f.write(f"  类: {element['classes']}\n")
                    f.write(f"  ID: {element['id']}\n")
                    f.write(f"  父元素: {element['parent']}\n")
                    f.write(f"  子元素数量: {element['children_count']}\n")
                    f.write(f"  属性: {element['attributes']}\n")
                    f.write("\n")
            
            print(f"\n✅ 所有开发者工具内容已保存到: {filename}")
            print(f"📊 内容统计:")
            print(f"   - HTML元素: {len(content_data['all_elements'])} 个")
            print(f"   - 文本内容: {len(content_data['all_text_content'])} 条")
            print(f"   - 链接: {len(content_data['all_links'])} 个")
            print(f"   - 图片: {len(content_data['all_images'])} 张")
            print(f"   - JavaScript: {len(content_data['all_scripts'])} 个")
            print(f"   - CSS: {len(content_data['all_styles'])} 个")
            print(f"   - 表单: {len(content_data['all_forms'])} 个")
            print(f"   - 输入元素: {len(content_data['all_inputs'])} 个")
            print(f"   - Meta标签: {len(content_data['all_meta_tags'])} 个")
            print(f"   - Data属性: {len(content_data['all_attributes'])} 个")
            
            return True
            
        except Exception as e:
            print(f"保存txt文件时出错: {e}")
            return False

def main():
    """主函数"""
    url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
    
    print("=== K3网站开发者工具完整内容提取器 ===\n")
    print(f"目标URL: {url}")
    
    extractor = DevToolsContentExtractor()
    
    # 提取所有内容
    print("\n开始提取页面的所有开发者工具内容...")
    content_data = extractor.extract_all_content(url)
    
    if content_data:
        # 保存到txt文件
        timestamp = int(time.time())
        filename = f'k3_devtools_complete_{timestamp}.txt'
        success = extractor.save_to_txt(content_data, filename)
        
        if success:
            print(f"\n✅ 开发者工具内容提取完成!")
            print(f"📁 文件已保存: {filename}")
        else:
            print(f"\n❌ 文件保存失败")
    else:
        print(f"\n❌ 内容提取失败")

if __name__ == "__main__":
    main()
