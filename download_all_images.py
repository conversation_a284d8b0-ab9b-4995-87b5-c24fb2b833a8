import requests
from bs4 import BeautifulSoup
import os
import time
from urllib.parse import urljoin, urlparse
import re

class ImageDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.k3.cn/',
        }
        self.session.headers.update(self.headers)
        self.base_url = 'https://www.k3.cn'
        self.images_folder = 'k3_images'
        self.create_images_folder()
        
    def create_images_folder(self):
        """创建图片下载文件夹"""
        if not os.path.exists(self.images_folder):
            os.makedirs(self.images_folder)
            print(f"创建图片文件夹: {self.images_folder}")
        else:
            print(f"图片文件夹已存在: {self.images_folder}")
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问页面: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            print(f"页面访问成功，状态码: {response.status_code}")
            print(f"页面大小: {len(response.content)} 字节")
            return response
        except Exception as e:
            print(f"页面访问失败: {e}")
            return None
    
    def extract_all_images(self, url):
        """提取页面中的所有图片URL"""
        response = self.get_page_content(url)
        if not response:
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        image_urls = []
        
        print("\n=== 提取图片URL ===")
        
        # 1. 提取所有img标签的src
        img_tags = soup.find_all('img')
        print(f"找到 {len(img_tags)} 个 <img> 标签")
        
        for i, img in enumerate(img_tags, 1):
            src = img.get('src', '')
            data_src = img.get('data-src', '')  # 懒加载图片
            alt = img.get('alt', '')
            title = img.get('title', '')
            
            # 处理src属性
            if src:
                full_url = self.normalize_url(src, url)
                if full_url:
                    image_urls.append({
                        'url': full_url,
                        'original_src': src,
                        'alt': alt,
                        'title': title,
                        'source': f'img_tag_{i}',
                        'type': 'img_src'
                    })
            
            # 处理data-src属性（懒加载）
            if data_src:
                full_url = self.normalize_url(data_src, url)
                if full_url:
                    image_urls.append({
                        'url': full_url,
                        'original_src': data_src,
                        'alt': alt,
                        'title': title,
                        'source': f'img_tag_{i}_data_src',
                        'type': 'img_data_src'
                    })
        
        # 2. 提取CSS中的背景图片
        print("提取CSS背景图片...")
        css_images = self.extract_css_background_images(soup, url)
        image_urls.extend(css_images)
        
        # 3. 提取内联样式中的背景图片
        print("提取内联样式背景图片...")
        inline_images = self.extract_inline_background_images(soup, url)
        image_urls.extend(inline_images)
        
        # 4. 提取其他可能的图片引用
        print("提取其他图片引用...")
        other_images = self.extract_other_image_references(soup, url)
        image_urls.extend(other_images)
        
        # 去重
        unique_images = self.remove_duplicates(image_urls)
        
        print(f"\n总共找到 {len(image_urls)} 个图片引用")
        print(f"去重后剩余 {len(unique_images)} 个唯一图片")
        
        return unique_images
    
    def normalize_url(self, src, base_url):
        """标准化URL"""
        if not src:
            return None
        
        # 移除空白字符
        src = src.strip()
        
        # 跳过data URL和无效URL
        if src.startswith('data:') or src.startswith('javascript:') or src == '#':
            return None
        
        # 处理相对URL
        if src.startswith('//'):
            return 'https:' + src
        elif src.startswith('/'):
            return urljoin(self.base_url, src)
        elif not src.startswith('http'):
            return urljoin(base_url, src)
        else:
            return src
    
    def extract_css_background_images(self, soup, base_url):
        """提取CSS中的背景图片"""
        css_images = []
        
        # 查找所有style标签
        style_tags = soup.find_all('style')
        for i, style in enumerate(style_tags, 1):
            if style.string:
                bg_images = re.findall(r'background-image:\s*url\(["\']?([^"\')\s]+)["\']?\)', style.string)
                bg_images.extend(re.findall(r'background:\s*[^;]*url\(["\']?([^"\')\s]+)["\']?\)', style.string))
                
                for j, img_url in enumerate(bg_images, 1):
                    full_url = self.normalize_url(img_url, base_url)
                    if full_url:
                        css_images.append({
                            'url': full_url,
                            'original_src': img_url,
                            'alt': '',
                            'title': '',
                            'source': f'css_style_{i}_bg_{j}',
                            'type': 'css_background'
                        })
        
        return css_images
    
    def extract_inline_background_images(self, soup, base_url):
        """提取内联样式中的背景图片"""
        inline_images = []
        
        # 查找所有带style属性的元素
        elements_with_style = soup.find_all(style=True)
        for i, element in enumerate(elements_with_style, 1):
            style_content = element.get('style', '')
            
            # 查找背景图片
            bg_images = re.findall(r'background-image:\s*url\(["\']?([^"\')\s]+)["\']?\)', style_content)
            bg_images.extend(re.findall(r'background:\s*[^;]*url\(["\']?([^"\')\s]+)["\']?\)', style_content))
            
            for j, img_url in enumerate(bg_images, 1):
                full_url = self.normalize_url(img_url, base_url)
                if full_url:
                    inline_images.append({
                        'url': full_url,
                        'original_src': img_url,
                        'alt': '',
                        'title': '',
                        'source': f'inline_style_{i}_bg_{j}',
                        'type': 'inline_background'
                    })
        
        return inline_images
    
    def extract_other_image_references(self, soup, base_url):
        """提取其他可能的图片引用"""
        other_images = []
        
        # 查找link标签中的图标
        link_tags = soup.find_all('link', rel=True)
        for i, link in enumerate(link_tags, 1):
            rel = link.get('rel', [])
            href = link.get('href', '')
            
            if any(r in ['icon', 'apple-touch-icon', 'shortcut icon'] for r in rel):
                full_url = self.normalize_url(href, base_url)
                if full_url:
                    other_images.append({
                        'url': full_url,
                        'original_src': href,
                        'alt': '',
                        'title': '',
                        'source': f'link_icon_{i}',
                        'type': 'icon'
                    })
        
        return other_images
    
    def remove_duplicates(self, image_list):
        """去除重复的图片URL"""
        seen_urls = set()
        unique_images = []
        
        for img in image_list:
            if img['url'] not in seen_urls:
                seen_urls.add(img['url'])
                unique_images.append(img)
        
        return unique_images
    
    def download_image(self, image_info, index):
        """下载单个图片"""
        url = image_info['url']
        source = image_info['source']
        img_type = image_info['type']
        
        try:
            print(f"正在下载图片 {index}: {url}")
            
            # 设置图片下载的headers
            img_headers = self.headers.copy()
            img_headers['Accept'] = 'image/webp,image/apng,image/*,*/*;q=0.8'
            
            response = self.session.get(url, headers=img_headers, timeout=15, stream=True)
            response.raise_for_status()
            
            # 检查是否是图片
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                print(f"  跳过非图片文件: {content_type}")
                return None
            
            # 确定文件扩展名
            ext = self.get_file_extension(url, content_type)
            
            # 生成安全的文件名
            filename = self.generate_safe_filename(url, source, img_type, ext, index)
            filepath = os.path.join(self.images_folder, filename)
            
            # 下载并保存图片
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print(f"  下载成功: {filename} ({file_size} 字节)")
            
            return {
                'filename': filename,
                'filepath': filepath,
                'url': url,
                'size': file_size,
                'type': img_type,
                'source': source
            }
            
        except Exception as e:
            print(f"  下载失败: {e}")
            return None
    
    def get_file_extension(self, url, content_type):
        """获取文件扩展名"""
        # 从content-type获取扩展名
        if 'jpeg' in content_type or 'jpg' in content_type:
            return '.jpg'
        elif 'png' in content_type:
            return '.png'
        elif 'gif' in content_type:
            return '.gif'
        elif 'webp' in content_type:
            return '.webp'
        elif 'svg' in content_type:
            return '.svg'
        elif 'ico' in content_type:
            return '.ico'
        
        # 从URL获取扩展名
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()
        if path.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.ico')):
            return os.path.splitext(path)[1]
        
        # 默认扩展名
        return '.jpg'
    
    def generate_safe_filename(self, url, source, img_type, ext, index):
        """生成安全的文件名"""
        # 从URL提取文件名
        parsed_url = urlparse(url)
        original_name = os.path.basename(parsed_url.path)
        
        if original_name and '.' in original_name:
            name_part = os.path.splitext(original_name)[0]
        else:
            name_part = f"{img_type}_{index}"
        
        # 清理文件名中的非法字符
        safe_name = re.sub(r'[^\w\-_.]', '_', name_part)
        safe_source = re.sub(r'[^\w\-_.]', '_', source)
        
        # 生成最终文件名
        filename = f"{index:03d}_{safe_name}_{safe_source}{ext}"
        
        # 确保文件名不会太长
        if len(filename) > 200:
            filename = f"{index:03d}_{img_type}_{safe_source[:50]}{ext}"
        
        return filename
    
    def download_all_images(self, url):
        """下载所有图片"""
        print("=== K3网站图片下载器 ===\n")
        print(f"目标URL: {url}")
        
        # 提取所有图片URL
        image_list = self.extract_all_images(url)
        
        if not image_list:
            print("未找到任何图片")
            return []
        
        print(f"\n=== 开始下载 {len(image_list)} 张图片 ===")
        
        downloaded_images = []
        failed_count = 0
        
        for i, image_info in enumerate(image_list, 1):
            print(f"\n[{i}/{len(image_list)}]")
            result = self.download_image(image_info, i)
            
            if result:
                downloaded_images.append(result)
            else:
                failed_count += 1
            
            # 添加延时避免请求过快
            time.sleep(0.5)
        
        # 生成下载报告
        self.generate_download_report(downloaded_images, failed_count, image_list)
        
        return downloaded_images
    
    def generate_download_report(self, downloaded_images, failed_count, total_images):
        """生成下载报告"""
        report_file = os.path.join(self.images_folder, 'download_report.txt')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=== K3网站图片下载报告 ===\n\n")
            f.write(f"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总图片数量: {len(total_images)}\n")
            f.write(f"成功下载: {len(downloaded_images)}\n")
            f.write(f"下载失败: {failed_count}\n")
            f.write(f"成功率: {len(downloaded_images)/len(total_images)*100:.1f}%\n\n")
            
            f.write("=== 成功下载的图片 ===\n")
            for img in downloaded_images:
                f.write(f"文件名: {img['filename']}\n")
                f.write(f"原始URL: {img['url']}\n")
                f.write(f"文件大小: {img['size']} 字节\n")
                f.write(f"图片类型: {img['type']}\n")
                f.write(f"来源: {img['source']}\n")
                f.write("-" * 50 + "\n")
        
        print(f"\n=== 下载完成 ===")
        print(f"📁 图片保存位置: {self.images_folder}")
        print(f"📊 下载统计:")
        print(f"   - 总图片数量: {len(total_images)}")
        print(f"   - 成功下载: {len(downloaded_images)}")
        print(f"   - 下载失败: {failed_count}")
        print(f"   - 成功率: {len(downloaded_images)/len(total_images)*100:.1f}%")
        print(f"📄 详细报告: {report_file}")

def main():
    """主函数"""
    url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
    
    downloader = ImageDownloader()
    downloaded_images = downloader.download_all_images(url)
    
    if downloaded_images:
        print(f"\n✅ 图片下载完成!")
        print(f"📁 请查看文件夹: {downloader.images_folder}")
    else:
        print(f"\n❌ 没有成功下载任何图片")

if __name__ == "__main__":
    main()
