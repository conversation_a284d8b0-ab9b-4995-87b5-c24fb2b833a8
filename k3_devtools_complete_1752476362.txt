================================================================================
K3网站开发者工具完整内容提取
================================================================================

URL: https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html
提取时间: 2025-07-14 14:59:22
HTTP状态码: 200
页面大小: 15068 字节

================================================== HTML结构 ==================================================
页面标题: 开山网用户登录
HTML标签属性: {}
Head内容长度: 1008
Body内容长度: 2586

================================================== HTTP响应头 ==================================================
Connection: close
Transfer-Encoding: chunked
Access-Control-Allow-Headers: X-Requested-With
Access-Control-Allow-Origin: *
Content-Type: text/html; charset=UTF-8
Date: Mon, 14 Jul 2025 06:59:22 GMT
Server: JYBC WEB/2.0

================================================== Meta标签 ==================================================
Meta 1:
  charset: utf-8
  attributes: {'charset': 'utf-8'}

Meta 2:
  name: format-detection
  content: email=no
  attributes: {'name': 'format-detection', 'content': 'email=no'}

Meta 3:
  content: text/html; charset=utf-8
  http_equiv: Content-Type
  attributes: {'http-equiv': 'Content-Type', 'content': 'text/html; charset=utf-8'}

Meta 4:
  content: IE=edge
  http_equiv: X-UA-Compatible
  attributes: {'http-equiv': 'X-UA-Compatible', 'content': 'IE=edge'}

Meta 5:
  name: format-detection
  content: telephone=no
  attributes: {'name': 'format-detection', 'content': 'telephone=no'}

Meta 6:
  name: viewport
  content: width=device-width,initial-scale=1
  attributes: {'name': 'viewport', 'content': 'width=device-width,initial-scale=1'}

Meta 7:
  name: renderer
  content: webkit
  attributes: {'name': 'renderer', 'content': 'webkit'}

================================================== 所有链接 ==================================================
链接 1:
  URL: http://www.k3.cn
  文本: 
  标题: 
  目标: 
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn'}

链接 2:
  URL: http://www.k3.cn
  文本: 返回首页
  标题: 
  目标: 
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn'}

链接 3:
  URL: http://www.k3.cn/api/chat/service
  文本: 找客服
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/api/chat/service', 'target': '_blank'}

链接 4:
  URL: http://www.k3.cn/api/chat/service
  文本: QQ联系
  标题: 
  目标: _blank
  类: ['QQ']
  ID: 
  所有属性: {'href': 'http://www.k3.cn/api/chat/service', 'target': '_blank', 'class': ['QQ']}

链接 5:
  URL: http://www.k3.cn/api/chat/service
  文本: 免费名片门牌
  标题: 
  目标: 
  类: ['btn']
  ID: 
  所有属性: {'class': ['btn'], 'href': 'http://www.k3.cn/api/chat/service'}

链接 6:
  URL: http://notice.k3.cn/pages/smzdm.html
  文本: 值得卖
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://notice.k3.cn/pages/smzdm.html', 'target': '_blank'}

链接 7:
  URL: http://www.k3.cn/thread.html
  文本: 供求信息
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/thread.html', 'target': '_blank'}

链接 8:
  URL: http://zhaopin.k3.cn/
  文本: 招工网
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://zhaopin.k3.cn/', 'target': '_blank'}

链接 9:
  URL: http://www.k3.cn/thread?&cata_id=105
  文本: 找款式
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/thread?&cata_id=105', 'target': '_blank'}

链接 10:
  URL: http://www.k3.cn/thread?&cata_id=103
  文本: 找房子
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/thread?&cata_id=103', 'target': '_blank'}

链接 11:
  URL: http://www.k3.cn/thread?&cata_id=102
  文本: 找二手
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/thread?&cata_id=102', 'target': '_blank'}

链接 12:
  URL: http://www.k3.cn/thread?&cata_id=100
  文本: 啥都有
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/thread?&cata_id=100', 'target': '_blank'}

链接 13:
  URL: http://www.k3.cn/default/help/fraud_prevention.html
  文本: 防骗专栏
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/default/help/fraud_prevention.html', 'target': '_blank'}

链接 14:
  URL: http://www.k3.cn/ajax/message/about_us?type=contacts
  文本: 联系我们
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/ajax/message/about_us?type=contacts', 'target': '_blank'}

链接 15:
  URL: javascript:void(0);
  文本: 手机APP
  标题: 
  目标: 
  类: []
  ID: 
  所有属性: {'href': 'javascript:void(0);'}

链接 16:
  URL: https://www.k3.cn/academy/index
  文本: 
  标题: 
  目标: 
  类: []
  ID: 
  所有属性: {'href': 'https://www.k3.cn/academy/index'}

链接 17:
  URL: javascript:void(0)
  文本: 刷新
  标题: 
  目标: 
  类: ['refresh-btn']
  ID: 
  所有属性: {'href': 'javascript:void(0)', 'class': ['refresh-btn'], 'style': 'outline:#6d6d6d none 0'}

链接 18:
  URL: https://www.k3.cn/auth/qq
  文本: QQ
  标题: 
  目标: 
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  所有属性: {'href': '/auth/qq', 'class': ['pdl', 'com-inline-block', 'com-relative']}

链接 19:
  URL: https://www.k3.cn/auth/wechat
  文本: 微信
  标题: 
  目标: 
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  所有属性: {'href': '/auth/wechat', 'class': ['pdl', 'com-inline-block', 'com-relative']}

链接 20:
  URL: http://reg.k3.cn
  文本: 立即注册
  标题: 
  目标: _blank
  类: ['com-color-red', 'com-font-size-14']
  ID: 
  所有属性: {'class': ['com-color-red', 'com-font-size-14'], 'href': 'http://reg.k3.cn', 'target': '_blank'}

链接 21:
  URL: javascript:void(0)
  文本: 获取验证码
  标题: 
  目标: 
  类: ['com-color-666', 'com-font-size-14']
  ID: refresh_mobile_vcode
  所有属性: {'href': 'javascript:void(0)', 'id': 'refresh_mobile_vcode', 'class': ['com-color-666', 'com-font-size-14']}

链接 22:
  URL: http://forgot.k3.cn
  文本: 忘记密码
  标题: 
  目标: _blank
  类: ['com-color-666']
  ID: 
  所有属性: {'href': 'http://forgot.k3.cn', 'class': ['com-color-666'], 'target': '_blank'}

链接 23:
  URL: #
  文本: 用其他账号登录
  标题: 
  目标: 
  类: []
  ID: 
  所有属性: {'href': '#'}

链接 24:
  URL: https://www.k3.cn/auth/qq
  文本: QQ
  标题: 
  目标: 
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  所有属性: {'href': '/auth/qq', 'class': ['pdl', 'com-inline-block', 'com-relative']}

链接 25:
  URL: https://www.k3.cn/auth/wechat
  文本: 微信
  标题: 
  目标: 
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  所有属性: {'href': '/auth/wechat', 'class': ['pdl', 'com-inline-block', 'com-relative']}

链接 26:
  URL: http://reg.k3.cn
  文本: 立即注册
  标题: 
  目标: _blank
  类: ['com-color-red', 'com-font-size-14']
  ID: 
  所有属性: {'class': ['com-color-red', 'com-font-size-14'], 'href': 'http://reg.k3.cn', 'target': '_blank'}

链接 27:
  URL: javascript:void(0)
  文本: 看不清换一张
  标题: 
  目标: 
  类: ['com-float-right', 'com-color-666', 'com-font-size-12', 'com-width-40']
  ID: refresh_vcode
  所有属性: {'href': 'javascript:void(0)', 'id': 'refresh_vcode', 'class': ['com-float-right', 'com-color-666', 'com-font-size-12', 'com-width-40']}

链接 28:
  URL: http://forgot.k3.cn
  文本: 忘记密码
  标题: 
  目标: _blank
  类: ['com-color-666']
  ID: 
  所有属性: {'href': 'http://forgot.k3.cn', 'class': ['com-color-666'], 'target': '_blank'}

链接 29:
  URL: #
  文本: 用其他账号登录
  标题: 
  目标: 
  类: []
  ID: 
  所有属性: {'href': '#'}

链接 30:
  URL: https://www.k3.cn/auth/qq
  文本: QQ
  标题: 
  目标: 
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  所有属性: {'href': '/auth/qq', 'class': ['pdl', 'com-inline-block', 'com-relative']}

链接 31:
  URL: https://www.k3.cn/auth/wechat
  文本: 微信
  标题: 
  目标: 
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  所有属性: {'href': '/auth/wechat', 'class': ['pdl', 'com-inline-block', 'com-relative']}

链接 32:
  URL: http://reg.k3.cn
  文本: 立即注册
  标题: 
  目标: _blank
  类: ['com-color-red', 'com-font-size-14']
  ID: 
  所有属性: {'class': ['com-color-red', 'com-font-size-14'], 'href': 'http://reg.k3.cn', 'target': '_blank'}

链接 33:
  URL: http://www.k3.cn/ajax/message/about_us?type=platform
  文本: 平台介绍
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/ajax/message/about_us?type=platform', 'type': 'platform', 'target': '_blank'}

链接 34:
  URL: http://www.k3.cn/ajax/message/about_us?type=contacts
  文本: 联系我们
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/ajax/message/about_us?type=contacts', 'type': 'contacts', 'target': '_blank'}

链接 35:
  URL: http://www.k3.cn/ajax/message/about_us?type=business
  文本: 商业合作
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/ajax/message/about_us?type=business', 'type': 'business', 'target': '_blank'}

链接 36:
  URL: http://notice.k3.cn/pages/smzdm.html
  文本: 什么值得卖
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://notice.k3.cn/pages/smzdm.html', 'target': '_blank'}

链接 37:
  URL: http://www.k3.cn/
  文本: 温岭女鞋
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.k3.cn/', 'target': '_blank'}

链接 38:
  URL: http://www.2tong.cn/
  文本: 温岭童鞋
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.2tong.cn/', 'target': '_blank'}

链接 39:
  URL: http://www.juyi5.cn/
  文本: 聚衣网
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.juyi5.cn/', 'target': '_blank'}

链接 40:
  URL: http://www.xingfujie.cn/
  文本: 福建男鞋
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.xingfujie.cn/', 'target': '_blank'}

链接 41:
  URL: http://www.bao66.cn/
  文本: 白沟网供
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.bao66.cn/', 'target': '_blank'}

链接 42:
  URL: http://www.yoduo.com/
  文本: 兴城泳装
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'href': 'http://www.yoduo.com/', 'target': '_blank'}

链接 43:
  URL: javascript:;
  文本: 服务热线 4006266818
  标题: 
  目标: 
  类: []
  ID: 
  所有属性: {'href': 'javascript:;'}

链接 44:
  URL: http://www.jybc.com.cn/
  文本: 版权所有©2025 北京聚源百成网络科技有限公司
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'target': '_blank', 'href': 'http://www.jybc.com.cn/', 'style': 'color:#666'}

链接 45:
  URL: http://beian.miit.gov.cn
  文本: 京ICP备14053148号-1
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'target': '_blank', 'href': 'http://beian.miit.gov.cn'}

链接 46:
  URL: http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802030672
  文本: 京公网安备 11010802030672号
  标题: 
  目标: _blank
  类: []
  ID: 
  所有属性: {'target': '_blank', 'href': 'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802030672'}

================================================== 所有图片 ==================================================
图片 1:
  URL: https://www.k3.cn/src/img/login/k3-logo.png
  Alt: 开山网用户登录
  标题: 
  尺寸:  x 30
  类: []
  ID: 
  所有属性: {'src': '/src/img/login/k3-logo.png', 'alt': '开山网用户登录', 'height': '30'}

图片 2:
  URL: https://www.k3.cn/dist/img/logo/mao.gif?v=1bc3cd666f
  Alt: 
  标题: 
  尺寸:  x 
  类: []
  ID: 
  所有属性: {'src': '/dist/img/logo/mao.gif?v=1bc3cd666f', 'style': 'margin-bottom:-12px;width:52px'}

图片 3:
  URL: https://www.k3.cn/dist/img/logo/qrcode-img.jpg?v=187cb523a5
  Alt: 
  标题: 
  尺寸:  x 
  类: []
  ID: 
  所有属性: {'src': '/dist/img/logo/qrcode-img.jpg?v=187cb523a5'}

图片 4:
  URL: https://www.k3.cn/src/img/login/k3-phone.png
  Alt: 
  标题: 
  尺寸:  x 
  类: ['com-float-left', 'phone-img']
  ID: 
  所有属性: {'class': ['com-float-left', 'phone-img'], 'src': '/src/img/login/k3-phone.png'}

图片 5:
  URL: https://www.k3.cn/dist/img/logo/secfull.png?v=f73c3e5300
  Alt: 
  标题: 
  尺寸:  x 
  类: []
  ID: 
  所有属性: {'src': '/dist/img/logo/secfull.png?v=f73c3e5300'}

图片 6:
  URL: https://www.k3.cn/dist/img/logo/sjh.png?v=139a32d805
  Alt: 
  标题: 
  尺寸:  x 
  类: []
  ID: 
  所有属性: {'src': '/dist/img/logo/sjh.png?v=139a32d805'}

图片 7:
  URL: https://www.k3.cn/dist/img/logo/yzm.png?v=2c51e466cc
  Alt: 
  标题: 
  尺寸:  x 
  类: []
  ID: 
  所有属性: {'src': '/dist/img/logo/yzm.png?v=2c51e466cc'}

图片 8:
  URL: http://misc.k3cdn.com/k/20191216/2019121614145277011767.png
  Alt: 
  标题: 
  尺寸:  x 
  类: []
  ID: 
  所有属性: {'src': 'http://misc.k3cdn.com/k/20191216/2019121614145277011767.png', 'style': 'vertical-align:sub'}

================================================== 所有表单 ==================================================
表单 1:
  Action: 
  Method: GET
  Enctype: 
  类: ['layui-form', 'layui-form-pane', 'formlogin', '']
  ID: 
  输入元素数量: 3
  所有属性: {'class': ['layui-form', 'layui-form-pane', 'formlogin', '']}

表单 2:
  Action: 
  Method: post
  Enctype: 
  类: ['faster_login', 'hidden']
  ID: 
  输入元素数量: 1
  所有属性: {'class': ['faster_login', 'hidden'], 'method': 'post'}

表单 3:
  Action: 
  Method: GET
  Enctype: 
  类: ['layui-form', 'layui-form-pane', 'formlogin', '']
  ID: 
  输入元素数量: 4
  所有属性: {'class': ['layui-form', 'layui-form-pane', 'formlogin', '']}

表单 4:
  Action: 
  Method: post
  Enctype: 
  类: ['faster_login', 'hidden']
  ID: 
  输入元素数量: 1
  所有属性: {'class': ['faster_login', 'hidden'], 'method': 'post'}

================================================== 所有输入元素 ==================================================
输入元素 1:
  标签: input
  类型: hidden
  名称: 
  值: 
  占位符: 
  类: []
  ID: domain-site
  必填: False
  禁用: False
  所有属性: {'type': 'hidden', 'id': 'domain-site', 'data-domain': 'k3'}

输入元素 2:
  标签: input
  类型: hidden
  名称: 
  值: L3NlYXJjaC93ZWIsYWxsLCVFOCVCRiU5MCVFNSU4QSVBOCVFOSU5RSU4QiwsMSwyLmh0bWw=
  占位符: 
  类: []
  ID: redirect_url
  必填: False
  禁用: False
  所有属性: {'type': 'hidden', 'id': 'redirect_url', 'value': 'L3NlYXJjaC93ZWIsYWxsLCVFOCVCRiU5MCVFNSU4QSVBOCVFOSU5RSU4QiwsMSwyLmh0bWw='}

输入元素 3:
  标签: input
  类型: text
  名称: username
  值: 
  占位符: 已验证手机
  类: ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14']
  ID: phone_name
  必填: True
  禁用: False
  所有属性: {'id': 'phone_name', 'title': '请填写已验证手机号', 'type': 'text', 'class': ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14'], 'name': 'username', 'placeholder': '已验证手机', 'required': '', 'value': '', 'tabindex': '1'}

输入元素 4:
  标签: input
  类型: text
  名称: phone_authcode
  值: 
  占位符: 
  类: ['itxt02', 'com-float-left', 'com-font-size-14']
  ID: phone_authcode
  必填: False
  禁用: False
  所有属性: {'id': 'phone_authcode', 'type': 'text', 'class': ['itxt02', 'com-float-left', 'com-font-size-14'], 'name': 'phone_authcode', 'tabindex': '2', 'style': 'width:200px'}

输入元素 5:
  标签: input
  类型: submit
  名称: 
  值: 登    录
  占位符: 
  类: []
  ID: mobile_login
  必填: False
  禁用: False
  所有属性: {'type': 'submit', 'id': 'mobile_login', 'lay-submit': '', 'value': '登\xa0\xa0\xa0\xa0录'}

输入元素 6:
  标签: input
  类型: submit
  名称: 
  值: 
  占位符: 
  类: ['submit', 'fast_login']
  ID: 
  必填: False
  禁用: False
  所有属性: {'type': 'submit', 'value': '', 'class': ['submit', 'fast_login']}

输入元素 7:
  标签: input
  类型: text
  名称: loginname
  值: 
  占位符: 用户名/已验证手机
  类: ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14']
  ID: loginname
  必填: True
  禁用: False
  所有属性: {'id': 'loginname', 'title': '请填写用户名/已验证手机', 'type': 'text', 'class': ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14'], 'name': 'loginname', 'placeholder': '用户名/已验证手机', 'required': '', 'value': '', 'tabindex': '1'}

输入元素 8:
  标签: input
  类型: password
  名称: nloginpwd
  值: 
  占位符: 密码
  类: ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14']
  ID: nloginpwd
  必填: True
  禁用: False
  所有属性: {'type': 'password', 'title': '请填写密码', 'id': 'nloginpwd', 'name': 'nloginpwd', 'class': ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14'], 'placeholder': '密码', 'required': '', 'value': '', 'tabindex': '2'}

输入元素 9:
  标签: input
  类型: text
  名称: authcode
  值: 
  占位符: 
  类: ['itxt02', 'com-float-left', 'com-margin-r-10', 'com-font-size-14']
  ID: authcode
  必填: False
  禁用: False
  所有属性: {'id': 'authcode', 'type': 'text', 'class': ['itxt02', 'com-float-left', 'com-margin-r-10', 'com-font-size-14'], 'maxlength': '4', 'name': 'authcode', 'tabindex': '3'}

输入元素 10:
  标签: input
  类型: submit
  名称: 
  值: 登    录
  占位符: 
  类: []
  ID: form_login
  必填: False
  禁用: False
  所有属性: {'type': 'submit', 'id': 'form_login', 'lay-submit': '', 'value': '登\xa0\xa0\xa0\xa0录'}

输入元素 11:
  标签: input
  类型: submit
  名称: 
  值: 
  占位符: 
  类: ['submit', 'fast_login']
  ID: 
  必填: False
  禁用: False
  所有属性: {'type': 'submit', 'value': '', 'class': ['submit', 'fast_login']}

输入元素 12:
  标签: input
  类型: text
  名称: 
  值: 
  占位符: 请输入验证码
  类: []
  ID: 
  必填: False
  禁用: False
  所有属性: {'type': 'text', 'maxlength': '6', 'placeholder': '请输入验证码'}

================================================== JavaScript内容 ==================================================
JavaScript 1 (来源: inline, 长度: 53):
window!=top&&(top.location.href=window.location.href)...

================================================== CSS内容 ==================================================
================================================== Data属性 ==================================================
Data属性 1:
  标签: input
  类: []
  ID: domain-site
  文本: 
  Data属性: {'data-domain': 'k3'}

Data属性 2:
  标签: script
  类: []
  ID: J_secure_sdk_v2
  文本: 
  Data属性: {'data-appkey': '12622718'}

================================================== 所有文本内容 ==================================================
文本 1 (父标签: [document], 类: ):
  doctype html

文本 3 (父标签: script, 类: ):
  window!=top&&(top.location.href=window.location.href)

文本 5 (父标签: div, 类: tips-wrapper):
  依据《网络安全法》，为保障您的账户安全和正常使用，请尽快完成手机号验证！将更有利于保护您的个人隐私。

文本 10 (父标签: p, 类: ):
  400-6266-818

文本 26 (父标签: div, 类: tips-wrapper com-font-size-12):
  开山网不会以任何理由要求您转账汇款，谨防诈骗。

文本 34 (父标签: p, 类: ):
  请勿刷新本页面，按手机提示操作！

文本 38 (父标签: div, 类: msg-error com-hide):
  公共场所不建议自动登录，以防账号丢失

文本 43 (父标签: div, 类: msg-error com-hide):
  公共场所不建议自动登录，以防账号丢失

文本 50 (父标签: b, 类: ):
  检测到您的账号登录地址异常

文本 51 (父标签: p, 类: ):
  为了您的账号安全请通过本次验证

文本 52 (父标签: b, 类: ):
  135****2305

文本 64 (父标签: a, 类: ):
  服务热线 4006266818

文本 65 (父标签: div, 类: layui-breadcrumb layui-text-center layui-footer copyright):
  ©2014-2025 www.k3.cn（开山网）

文本 66 (父标签: a, 类: ):
  版权所有©2025 北京聚源百成网络科技有限公司

文本 67 (父标签: a, 类: ):
  京ICP备14053148号-1

文本 68 (父标签: a, 类: ):
  京公网安备 11010802030672号

文本 69 (父标签: div, 类: layui-breadcrumb layui-text-center layui-footer copyright):
  投诉电话：184 0178 7818

================================================== 所有HTML元素 ==================================================
元素 1:
  标签: html
  文本: 开山网用户登录window!=top&&(top.location.href=window.location.href)返回首页i依据《网络安全法》，为保障您的账户安全和正常使用，请尽快完成手机号验证！将更有利于保护您的个人隐私。找客服服务时间：9:00-22:00客服电话400-6266-818在线咨询QQ联系快速通道免费名片门牌值得卖供求信息招工网找款式找房子找二手啥都有防骗专栏联系我们手机A
  类: []
  ID: 
  父元素: [document]
  子元素数量: 79
  属性: {}

元素 2:
  标签: head
  文本: 开山网用户登录window!=top&&(top.location.href=window.location.href)
  类: []
  ID: 
  父元素: html
  子元素数量: 16
  属性: {}

元素 3:
  标签: meta
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'charset': 'utf-8'}

元素 4:
  标签: meta
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'name': 'format-detection', 'content': 'email=no'}

元素 5:
  标签: meta
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'http-equiv': 'Content-Type', 'content': 'text/html; charset=utf-8'}

元素 6:
  标签: meta
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'http-equiv': 'X-UA-Compatible', 'content': 'IE=edge'}

元素 7:
  标签: meta
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'name': 'format-detection', 'content': 'telephone=no'}

元素 8:
  标签: meta
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'name': 'viewport', 'content': 'width=device-width,initial-scale=1'}

元素 9:
  标签: meta
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'name': 'renderer', 'content': 'webkit'}

元素 10:
  标签: title
  文本: 开山网用户登录
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {}

元素 11:
  标签: link
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'rel': ['apple-touch-icon'], 'href': '/src/img/icon/k3-app.png'}

元素 12:
  标签: link
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'rel': ['shortcut', 'icon'], 'href': '/src/img/icon/k3.ico'}

元素 13:
  标签: link
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'rel': ['stylesheet'], 'type': 'text/css', 'href': '/font_2483247_74mhc8ya0s/iconfont.css'}

元素 14:
  标签: link
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'rel': ['stylesheet'], 'type': 'text/css', 'href': '/passport/layui/css/layui.css'}

元素 15:
  标签: script
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'type': 'text/javascript', 'src': '//public.k3cdn.com/jquery/2.2.4/jquery.min.js'}

元素 16:
  标签: script
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'type': 'text/javascript', 'src': '/passport/layui/layui.js'}

元素 17:
  标签: link
  文本: 
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'href': '/dist/css/public.css?v=09f2784c40', 'rel': ['stylesheet'], 'type': 'text/css'}

元素 18:
  标签: script
  文本: window!=top&&(top.location.href=window.location.href)
  类: []
  ID: 
  父元素: head
  子元素数量: 0
  属性: {'type': 'text/javascript'}

元素 19:
  标签: body
  文本: 返回首页i依据《网络安全法》，为保障您的账户安全和正常使用，请尽快完成手机号验证！将更有利于保护您的个人隐私。找客服服务时间：9:00-22:00客服电话400-6266-818在线咨询QQ联系快速通道免费名片门牌值得卖供求信息招工网找款式找房子找二手啥都有防骗专栏联系我们手机APP扫码安装APP
  类: ['layout_k3']
  ID: 
  父元素: html
  子元素数量: 61
  属性: {'class': ['layout_k3']}

元素 20:
  标签: div
  文本: 返回首页
  类: ['header_list_box']
  ID: 
  父元素: body
  子元素数量: 6
  属性: {'class': ['header_list_box']}

元素 21:
  标签: ul
  文本: 返回首页
  类: ['nav-content']
  ID: 
  父元素: div
  子元素数量: 5
  属性: {'class': ['nav-content']}

元素 22:
  标签: li
  文本: 
  类: ['com-float-left', 'header-nav', 'site_logo']
  ID: 
  父元素: ul
  子元素数量: 2
  属性: {'class': ['com-float-left', 'header-nav', 'site_logo']}

元素 23:
  标签: a
  文本: 
  类: []
  ID: 
  父元素: li
  子元素数量: 1
  属性: {'href': 'http://www.k3.cn'}

元素 24:
  标签: img
  文本: 
  类: []
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'src': '/src/img/login/k3-logo.png', 'alt': '开山网用户登录', 'height': '30'}

元素 25:
  标签: li
  文本: 返回首页
  类: ['com-float-left', 'header-nav', 'return_head']
  ID: 
  父元素: ul
  子元素数量: 1
  属性: {'class': ['com-float-left', 'header-nav', 'return_head']}

元素 26:
  标签: a
  文本: 返回首页
  类: []
  ID: 
  父元素: li
  子元素数量: 0
  属性: {'href': 'http://www.k3.cn'}

元素 27:
  标签: div
  文本: i依据《网络安全法》，为保障您的账户安全和正常使用，请尽快完成手机号验证！将更有利于保护您的个人隐私。
  类: ['tips-wrapper']
  ID: 
  父元素: body
  子元素数量: 2
  属性: {'class': ['tips-wrapper']}

元素 28:
  标签: span
  文本: i
  类: ['com-tips-i']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['com-tips-i']}

元素 29:
  标签: p
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {}

元素 30:
  标签: div
  文本: 找客服服务时间：9:00-22:00客服电话400-6266-818在线咨询QQ联系快速通道免费名片门牌值得卖供求信息招工网找款式找房子找二手啥都有防骗专栏联系我们手机APP扫码安装APP
  类: ['sidebar-box']
  ID: 
  父元素: body
  子元素数量: 50
  属性: {'class': ['sidebar-box']}

元素 31:
  标签: div
  文本: 找客服服务时间：9:00-22:00客服电话400-6266-818在线咨询QQ联系快速通道免费名片门牌值得卖供求信息招工网找款式找房子找二手啥都有防骗专栏联系我们手机APP扫码安装APP
  类: ['sidebar']
  ID: 
  父元素: div
  子元素数量: 49
  属性: {'class': ['sidebar']}

元素 32:
  标签: img
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'src': '/dist/img/logo/mao.gif?v=1bc3cd666f', 'style': 'margin-bottom:-12px;width:52px'}

元素 33:
  标签: div
  文本: 找客服服务时间：9:00-22:00客服电话400-6266-818在线咨询QQ联系快速通道免费名片门牌值得卖供求信息招工网找款式找房子找二手啥都有防骗专栏联系我们手机APP扫码安装APP
  类: ['sidebar-action']
  ID: 
  父元素: div
  子元素数量: 47
  属性: {'class': ['sidebar-action']}

元素 34:
  标签: div
  文本: 找客服服务时间：9:00-22:00客服电话400-6266-818在线咨询QQ联系快速通道免费名片门牌
  类: ['sidebar-item']
  ID: 
  父元素: div
  子元素数量: 15
  属性: {'class': ['sidebar-item']}

元素 35:
  标签: a
  文本: 找客服
  类: []
  ID: 
  父元素: div
  子元素数量: 2
  属性: {'href': 'http://www.k3.cn/api/chat/service', 'target': '_blank'}

元素 36:
  标签: i
  文本: 
  类: ['iconfont', 'iconsidebar-01']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['iconfont', 'iconsidebar-01']}

元素 37:
  标签: p
  文本: 找客服
  类: []
  ID: 
  父元素: a
  子元素数量: 0
  属性: {}

元素 38:
  标签: div
  文本: 服务时间：9:00-22:00客服电话400-6266-818在线咨询QQ联系快速通道免费名片门牌
  类: ['customQQ', 'sub-menu']
  ID: 
  父元素: div
  子元素数量: 11
  属性: {'class': ['customQQ', 'sub-menu']}

元素 39:
  标签: p
  文本: 
  类: ['line']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['line']}

元素 40:
  标签: p
  文本: 服务时间：9:00-22:00
  类: ['time']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['time']}

元素 41:
  标签: span
  文本: 9:00-22:00
  类: []
  ID: 
  父元素: p
  子元素数量: 0
  属性: {'style': 'color:#333'}

元素 42:
  标签: p
  文本: 客服电话
  类: ['text']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['text']}

元素 43:
  标签: p
  文本: 400-6266-818
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'style': 'font-weight:700;font-size:12px;color:#a02124'}

元素 44:
  标签: p
  文本: 在线咨询
  类: ['text']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['text']}

元素 45:
  标签: a
  文本: QQ联系
  类: ['QQ']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'href': 'http://www.k3.cn/api/chat/service', 'target': '_blank', 'class': ['QQ']}

元素 46:
  标签: i
  文本: 
  类: ['iconfont', 'iconqq']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['iconfont', 'iconqq'], 'style': 'font-size:12px'}

元素 47:
  标签: p
  文本: 快速通道
  类: ['text']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['text']}

元素 48:
  标签: a
  文本: 免费名片门牌
  类: ['btn']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['btn'], 'href': 'http://www.k3.cn/api/chat/service'}

元素 49:
  标签: p
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {}

元素 50:
  标签: div
  文本: 值得卖
  类: ['sidebar-item']
  ID: 
  父元素: div
  子元素数量: 3
  属性: {'class': ['sidebar-item']}

元素 51:
  标签: a
  文本: 值得卖
  类: []
  ID: 
  父元素: div
  子元素数量: 2
  属性: {'href': 'http://notice.k3.cn/pages/smzdm.html', 'target': '_blank'}

元素 52:
  标签: i
  文本: 
  类: ['iconfont', 'iconsidebar-03']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['iconfont', 'iconsidebar-03']}

元素 53:
  标签: p
  文本: 值得卖
  类: []
  ID: 
  父元素: a
  子元素数量: 0
  属性: {}

元素 54:
  标签: div
  文本: 供求信息招工网找款式找房子找二手啥都有
  类: ['sidebar-item']
  ID: 
  父元素: div
  子元素数量: 10
  属性: {'class': ['sidebar-item']}

元素 55:
  标签: a
  文本: 供求信息
  类: []
  ID: 
  父元素: div
  子元素数量: 2
  属性: {'href': 'http://www.k3.cn/thread.html', 'target': '_blank'}

元素 56:
  标签: i
  文本: 
  类: ['iconfont', 'iconsidebar-04']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['iconfont', 'iconsidebar-04']}

元素 57:
  标签: p
  文本: 供求信息
  类: []
  ID: 
  父元素: a
  子元素数量: 0
  属性: {}

元素 58:
  标签: div
  文本: 招工网找款式找房子找二手啥都有
  类: ['gongqiu', 'sub-menu']
  ID: 
  父元素: div
  子元素数量: 6
  属性: {'class': ['gongqiu', 'sub-menu']}

元素 59:
  标签: p
  文本: 
  类: ['line']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['line']}

元素 60:
  标签: a
  文本: 招工网
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://zhaopin.k3.cn/', 'target': '_blank'}

元素 61:
  标签: a
  文本: 找款式
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.k3.cn/thread?&cata_id=105', 'target': '_blank'}

元素 62:
  标签: a
  文本: 找房子
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.k3.cn/thread?&cata_id=103', 'target': '_blank'}

元素 63:
  标签: a
  文本: 找二手
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.k3.cn/thread?&cata_id=102', 'target': '_blank'}

元素 64:
  标签: a
  文本: 啥都有
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.k3.cn/thread?&cata_id=100', 'target': '_blank'}

元素 65:
  标签: div
  文本: 防骗专栏
  类: ['sidebar-item']
  ID: 
  父元素: div
  子元素数量: 3
  属性: {'class': ['sidebar-item']}

元素 66:
  标签: a
  文本: 防骗专栏
  类: []
  ID: 
  父元素: div
  子元素数量: 2
  属性: {'href': 'http://www.k3.cn/default/help/fraud_prevention.html', 'target': '_blank'}

元素 67:
  标签: i
  文本: 
  类: ['iconfont', 'iconsidebar-05']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['iconfont', 'iconsidebar-05']}

元素 68:
  标签: p
  文本: 防骗专栏
  类: []
  ID: 
  父元素: a
  子元素数量: 0
  属性: {}

元素 69:
  标签: div
  文本: 联系我们
  类: ['sidebar-item']
  ID: 
  父元素: div
  子元素数量: 3
  属性: {'class': ['sidebar-item']}

元素 70:
  标签: a
  文本: 联系我们
  类: []
  ID: 
  父元素: div
  子元素数量: 2
  属性: {'href': 'http://www.k3.cn/ajax/message/about_us?type=contacts', 'target': '_blank'}

元素 71:
  标签: i
  文本: 
  类: ['iconfont', 'iconsidebar-02']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['iconfont', 'iconsidebar-02']}

元素 72:
  标签: p
  文本: 联系我们
  类: []
  ID: 
  父元素: a
  子元素数量: 0
  属性: {}

元素 73:
  标签: div
  文本: 手机APP扫码安装APP
  类: ['sidebar-item']
  ID: 
  父元素: div
  子元素数量: 7
  属性: {'class': ['sidebar-item']}

元素 74:
  标签: a
  文本: 手机APP
  类: []
  ID: 
  父元素: div
  子元素数量: 2
  属性: {'href': 'javascript:void(0);'}

元素 75:
  标签: i
  文本: 
  类: ['iconfont', 'iconsidebar-07']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['iconfont', 'iconsidebar-07']}

元素 76:
  标签: p
  文本: 手机APP
  类: []
  ID: 
  父元素: a
  子元素数量: 0
  属性: {}

元素 77:
  标签: div
  文本: 扫码安装APP
  类: ['erweima', 'sub-menu']
  ID: 
  父元素: div
  子元素数量: 3
  属性: {'class': ['erweima', 'sub-menu']}

元素 78:
  标签: p
  文本: 
  类: ['line']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['line']}

元素 79:
  标签: p
  文本: 
  类: ['qrcode-bg']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['qrcode-bg'], 'style': 'background-image:url(/src/img/login/k3_app.jpg)'}

元素 80:
  标签: div
  文本: 扫码安装APP
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'style': 'color:#f04848'}

元素 81:
  标签: link
  文本: 
  类: []
  ID: 
  父元素: [document]
  子元素数量: 0
  属性: {'href': '/dist/css/login.css?v=74b784ee3f', 'rel': ['stylesheet'], 'type': 'text/css'}

元素 82:
  标签: input
  文本: 
  类: []
  ID: domain-site
  父元素: [document]
  子元素数量: 0
  属性: {'type': 'hidden', 'id': 'domain-site', 'data-domain': 'k3'}

元素 83:
  标签: input
  文本: 
  类: []
  ID: redirect_url
  父元素: [document]
  子元素数量: 0
  属性: {'type': 'hidden', 'id': 'redirect_url', 'value': 'L3NlYXJjaC93ZWIsYWxsLCVFOCVCRiU5MCVFNSU4QSVBOCVFOSU5RSU4QiwsMSwyLmh0bWw='}

元素 84:
  标签: div
  文本: i开山网不会以任何理由要求您转账汇款，谨防诈骗。扫码登录|短信登录|账户登录二维码已失效刷新打开手机开山网扫描二维码扫描成功！请勿刷新本页面，按手机提示操作！免输入更快更安全QQ|微信立即注册公共场所不建议自动登录，以防账号丢失获取验证码忘记密码用其他账号登录QQ|微信立即注册公共场所不建议自动登录，以防账号丢失大小写锁定已打开看不清换一张忘记密码用其他账号登录QQ|微信立即注册登录成功
  类: ['layui-tab', 'layui-tab-brief', 'login_wrap']
  ID: 
  父元素: [document]
  子元素数量: 139
  属性: {'class': ['layui-tab', 'layui-tab-brief', 'login_wrap']}

元素 85:
  标签: div
  文本: 
  类: ['layui-tab', 'layui-tab-brief', 'login-background']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['layui-tab', 'layui-tab-brief', 'login-background'], 'style': 'background-image:url(http://misc.k3cdn.com/k/20250604/2025060415202065883222w2500h600.jpg);background-color:#1124B5'}

元素 86:
  标签: div
  文本: 
  类: ['picture_href']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['picture_href']}

元素 87:
  标签: a
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'https://www.k3.cn/academy/index'}

元素 88:
  标签: div
  文本: i开山网不会以任何理由要求您转账汇款，谨防诈骗。扫码登录|短信登录|账户登录二维码已失效刷新打开手机开山网扫描二维码扫描成功！请勿刷新本页面，按手机提示操作！免输入更快更安全QQ|微信立即注册公共场所不建议自动登录，以防账号丢失获取验证码忘记密码用其他账号登录QQ|微信立即注册公共场所不建议自动登录，以防账号丢失大小写锁定已打开看不清换一张忘记密码用其他账号登录QQ|微信立即注册登录成功
  类: ['login_content']
  ID: 
  父元素: div
  子元素数量: 135
  属性: {'class': ['login_content']}

元素 89:
  标签: div
  文本: i开山网不会以任何理由要求您转账汇款，谨防诈骗。扫码登录|短信登录|账户登录二维码已失效刷新打开手机开山网扫描二维码扫描成功！请勿刷新本页面，按手机提示操作！免输入更快更安全QQ|微信立即注册公共场所不建议自动登录，以防账号丢失获取验证码忘记密码用其他账号登录QQ|微信立即注册公共场所不建议自动登录，以防账号丢失大小写锁定已打开看不清换一张忘记密码用其他账号登录QQ|微信立即注册登录成功
  类: ['login_box']
  ID: 
  父元素: div
  子元素数量: 134
  属性: {'class': ['login_box']}

元素 90:
  标签: div
  文本: i开山网不会以任何理由要求您转账汇款，谨防诈骗。
  类: ['tips-wrapper', 'com-font-size-12']
  ID: 
  父元素: div
  子元素数量: 2
  属性: {'class': ['tips-wrapper', 'com-font-size-12']}

元素 91:
  标签: span
  文本: i
  类: ['com-tips-i']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['com-tips-i']}

元素 92:
  标签: p
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {}

元素 93:
  标签: div
  文本: 扫码登录|短信登录|账户登录二维码已失效刷新打开手机开山网扫描二维码扫描成功！请勿刷新本页面，按手机提示操作！免输入更快更安全QQ|微信立即注册公共场所不建议自动登录，以防账号丢失获取验证码忘记密码用其他账号登录QQ|微信立即注册公共场所不建议自动登录，以防账号丢失大小写锁定已打开看不清换一张忘记密码用其他账号登录QQ|微信立即注册登录成功
  类: ['layui-tab', 'login-tab-box']
  ID: 
  父元素: div
  子元素数量: 130
  属性: {'class': ['layui-tab', 'login-tab-box'], 'lay-filter': 'docDemoTabBrief'}

元素 94:
  标签: ul
  文本: 扫码登录|短信登录|账户登录
  类: ['layui-tab-title']
  ID: 
  父元素: div
  子元素数量: 5
  属性: {'class': ['layui-tab-title']}

元素 95:
  标签: li
  文本: 扫码登录|
  类: ['login-tab', 'com-font-microsoft', 'layui-text-center', 'layui-bg-white']
  ID: 
  父元素: ul
  子元素数量: 1
  属性: {'class': ['login-tab', 'com-font-microsoft', 'layui-text-center', 'layui-bg-white']}

元素 96:
  标签: span
  文本: |
  类: []
  ID: 
  父元素: li
  子元素数量: 0
  属性: {'style': 'float:right;font-weight:100'}

元素 97:
  标签: li
  文本: 短信登录|
  类: ['login-tab', 'com-font-microsoft', 'layui-text-center', 'layui-bg-white']
  ID: 
  父元素: ul
  子元素数量: 1
  属性: {'class': ['login-tab', 'com-font-microsoft', 'layui-text-center', 'layui-bg-white']}

元素 98:
  标签: span
  文本: |
  类: []
  ID: 
  父元素: li
  子元素数量: 0
  属性: {'style': 'float:right;font-weight:100'}

元素 99:
  标签: li
  文本: 账户登录
  类: ['layui-this', 'login-tab', 'com-font-microsoft', 'layui-text-center', 'layui-bg-white']
  ID: 
  父元素: ul
  子元素数量: 0
  属性: {'class': ['layui-this', 'login-tab', 'com-font-microsoft', 'layui-text-center', 'layui-bg-white']}

元素 100:
  标签: div
  文本: 二维码已失效刷新打开手机开山网扫描二维码扫描成功！请勿刷新本页面，按手机提示操作！免输入更快更安全QQ|微信立即注册公共场所不建议自动登录，以防账号丢失获取验证码忘记密码用其他账号登录QQ|微信立即注册公共场所不建议自动登录，以防账号丢失大小写锁定已打开看不清换一张忘记密码用其他账号登录QQ|微信立即注册登录成功
  类: ['layui-tab-content']
  ID: 
  父元素: div
  子元素数量: 123
  属性: {'class': ['layui-tab-content']}

元素 101:
  标签: div
  文本: 二维码已失效刷新打开手机开山网扫描二维码扫描成功！请勿刷新本页面，按手机提示操作！免输入更快更安全QQ|微信立即注册
  类: ['layui-tab-item', 'qrcode-login']
  ID: 
  父元素: div
  子元素数量: 43
  属性: {'class': ['layui-tab-item', 'qrcode-login']}

元素 102:
  标签: div
  文本: 二维码已失效刷新打开手机开山网扫描二维码扫描成功！请勿刷新本页面，按手机提示操作！免输入更快更安全
  类: ['com-padding-20', 'com-relative', 'com-inline-block']
  ID: 
  父元素: div
  子元素数量: 28
  属性: {'class': ['com-padding-20', 'com-relative', 'com-inline-block']}

元素 103:
  标签: div
  文本: 二维码已失效刷新打开手机开山网扫描二维码
  类: ['invalid-show']
  ID: 
  父元素: div
  子元素数量: 10
  属性: {'class': ['invalid-show']}

元素 104:
  标签: div
  文本: 二维码已失效刷新
  类: ['invalid']
  ID: 
  父元素: div
  子元素数量: 3
  属性: {'class': ['invalid']}

元素 105:
  标签: div
  文本: 
  类: ['failure-mask']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['failure-mask']}

元素 106:
  标签: p
  文本: 二维码已失效
  类: ['err-cont']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['err-cont']}

元素 107:
  标签: a
  文本: 刷新
  类: ['refresh-btn']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'javascript:void(0)', 'class': ['refresh-btn'], 'style': 'outline:#6d6d6d none 0'}

元素 108:
  标签: div
  文本: 
  类: ['com-inline-block', 'qrcode-img']
  ID: 
  父元素: div
  子元素数量: 3
  属性: {'class': ['com-inline-block', 'qrcode-img']}

元素 109:
  标签: div
  文本: 
  类: ['com-float-left', 'QR-code']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['com-float-left', 'QR-code']}

元素 110:
  标签: img
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'src': '/dist/img/logo/qrcode-img.jpg?v=187cb523a5'}

元素 111:
  标签: img
  文本: 
  类: ['com-float-left', 'phone-img']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['com-float-left', 'phone-img'], 'src': '/src/img/login/k3-phone.png'}

元素 112:
  标签: div
  文本: 打开手机开山网扫描二维码
  类: ['layui-text-center', 'com-color-666', 'com-font-size-12', 'com-mag-15-auto']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['layui-text-center', 'com-color-666', 'com-font-size-12', 'com-mag-15-auto']}

元素 113:
  标签: font
  文本: 手机开山网
  类: ['com-color-red', 'com-font-size-12']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['com-color-red', 'com-font-size-12']}

元素 114:
  标签: div
  文本: 扫描成功！请勿刷新本页面，按手机提示操作！
  类: ['_after']
  ID: show-msg
  父元素: div
  子元素数量: 5
  属性: {'class': ['_after'], 'id': 'show-msg', 'style': 'display:none'}

元素 115:
  标签: div
  文本: 
  类: ['log_img1']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['log_img1']}

元素 116:
  标签: img
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'src': '/dist/img/logo/secfull.png?v=f73c3e5300'}

元素 117:
  标签: div
  文本: 扫描成功！请勿刷新本页面，按手机提示操作！
  类: ['success_txt']
  ID: 
  父元素: div
  子元素数量: 2
  属性: {'class': ['success_txt']}

元素 118:
  标签: p
  文本: 扫描成功！
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {}

元素 119:
  标签: p
  文本: 请勿刷新本页面，按手机提示操作！
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {}

元素 120:
  标签: div
  文本: 免输入更快更安全
  类: ['layui-bg-white', 'com-font-microsoft', 'qr-coagent']
  ID: 
  父元素: div
  子元素数量: 10
  属性: {'class': ['layui-bg-white', 'com-font-microsoft', 'qr-coagent']}

元素 121:
  标签: ul
  文本: 免输入更快更安全
  类: []
  ID: 
  父元素: div
  子元素数量: 9
  属性: {}

元素 122:
  标签: li
  文本: 免输入
  类: []
  ID: 
  父元素: ul
  子元素数量: 2
  属性: {}

元素 123:
  标签: b
  文本: 
  类: ['lauyui-nav-left', 'com-pen-icon']
  ID: 
  父元素: li
  子元素数量: 0
  属性: {'class': ['lauyui-nav-left', 'com-pen-icon']}

元素 124:
  标签: em
  文本: 免输入
  类: []
  ID: 
  父元素: li
  子元素数量: 0
  属性: {}

元素 125:
  标签: li
  文本: 更快
  类: []
  ID: 
  父元素: ul
  子元素数量: 2
  属性: {}

元素 126:
  标签: b
  文本: 
  类: ['lauyui-nav-left', 'com-fast-icon']
  ID: 
  父元素: li
  子元素数量: 0
  属性: {'class': ['lauyui-nav-left', 'com-fast-icon']}

元素 127:
  标签: em
  文本: 更快
  类: []
  ID: 
  父元素: li
  子元素数量: 0
  属性: {}

元素 128:
  标签: li
  文本: 更安全
  类: []
  ID: 
  父元素: ul
  子元素数量: 2
  属性: {}

元素 129:
  标签: b
  文本: 
  类: ['lauyui-nav-left', 'com-safe-icon']
  ID: 
  父元素: li
  子元素数量: 0
  属性: {'class': ['lauyui-nav-left', 'com-safe-icon']}

元素 130:
  标签: em
  文本: 更安全
  类: []
  ID: 
  父元素: li
  子元素数量: 0
  属性: {}

元素 131:
  标签: div
  文本: QQ|微信立即注册
  类: ['com-pdl-pdr-20', 'third-party']
  ID: 
  父元素: div
  子元素数量: 13
  属性: {'class': ['com-pdl-pdr-20', 'third-party']}

元素 132:
  标签: ul
  文本: QQ|微信立即注册
  类: []
  ID: 
  父元素: div
  子元素数量: 12
  属性: {}

元素 133:
  标签: li
  文本: QQ|
  类: ['com-float-left']
  ID: 
  父元素: ul
  子元素数量: 4
  属性: {'class': ['com-float-left']}

元素 134:
  标签: a
  文本: QQ
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  父元素: li
  子元素数量: 2
  属性: {'href': '/auth/qq', 'class': ['pdl', 'com-inline-block', 'com-relative']}

元素 135:
  标签: b
  文本: 
  类: ['com-QQ-icon']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-QQ-icon']}

元素 136:
  标签: span
  文本: QQ
  类: ['com-font-size-12', 'com-color-666']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-font-size-12', 'com-color-666']}

元素 137:
  标签: span
  文本: |
  类: ['com-line']
  ID: 
  父元素: li
  子元素数量: 0
  属性: {'class': ['com-line']}

元素 138:
  标签: li
  文本: 微信
  类: ['com-float-left']
  ID: 
  父元素: ul
  子元素数量: 3
  属性: {'class': ['com-float-left']}

元素 139:
  标签: a
  文本: 微信
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  父元素: li
  子元素数量: 2
  属性: {'href': '/auth/wechat', 'class': ['pdl', 'com-inline-block', 'com-relative']}

元素 140:
  标签: b
  文本: 
  类: ['com-weixin-icon']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-weixin-icon']}

元素 141:
  标签: span
  文本: 微信
  类: ['com-font-size-12', 'com-color-666']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-font-size-12', 'com-color-666']}

元素 142:
  标签: li
  文本: 立即注册
  类: ['com-float-right']
  ID: 
  父元素: ul
  子元素数量: 2
  属性: {'class': ['com-float-right']}

元素 143:
  标签: a
  文本: 立即注册
  类: ['com-color-red', 'com-font-size-14']
  ID: 
  父元素: li
  子元素数量: 1
  属性: {'class': ['com-color-red', 'com-font-size-14'], 'href': 'http://reg.k3.cn', 'target': '_blank'}

元素 144:
  标签: b
  文本: 
  类: ['regist-link']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['regist-link']}

元素 145:
  标签: div
  文本: 公共场所不建议自动登录，以防账号丢失获取验证码忘记密码用其他账号登录QQ|微信立即注册
  类: ['layui-tab-item', 'mobile-login']
  ID: 
  父元素: div
  子元素数量: 34
  属性: {'class': ['layui-tab-item', 'mobile-login']}

元素 146:
  标签: div
  文本: 公共场所不建议自动登录，以防账号丢失获取验证码忘记密码用其他账号登录
  类: ['com-padding-20', 'com-relative', 'com-inline-block', 'account-box']
  ID: 
  父元素: div
  子元素数量: 19
  属性: {'class': ['com-padding-20', 'com-relative', 'com-inline-block', 'account-box']}

元素 147:
  标签: div
  文本: 公共场所不建议自动登录，以防账号丢失
  类: ['msg-wrap']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['msg-wrap']}

元素 148:
  标签: div
  文本: 公共场所不建议自动登录，以防账号丢失
  类: ['msg-error', 'com-hide']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['msg-error', 'com-hide']}

元素 149:
  标签: form
  文本: 获取验证码忘记密码
  类: ['layui-form', 'layui-form-pane', 'formlogin', '']
  ID: 
  父元素: div
  子元素数量: 12
  属性: {'class': ['layui-form', 'layui-form-pane', 'formlogin', '']}

元素 150:
  标签: div
  文本: 
  类: ['com-relative', 'com-z-index-1', 'com-margin-b-20', 'layui-user-login']
  ID: 
  父元素: form
  子元素数量: 3
  属性: {'class': ['com-relative', 'com-z-index-1', 'com-margin-b-20', 'layui-user-login']}

元素 151:
  标签: label
  文本: 
  类: ['layui-login-label', 'layui-name-label', 'lauyui-nav-left']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'for': 'phone_name', 'class': ['layui-login-label', 'layui-name-label', 'lauyui-nav-left']}

元素 152:
  标签: input
  文本: 
  类: ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14']
  ID: phone_name
  父元素: div
  子元素数量: 0
  属性: {'id': 'phone_name', 'title': '请填写已验证手机号', 'type': 'text', 'class': ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14'], 'name': 'username', 'placeholder': '已验证手机', 'required': '', 'value': '', 'tabindex': '1'}

元素 153:
  标签: span
  文本: 
  类: ['clear-btn']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['clear-btn'], 'onclick': '$("#phone_name").val("")'}

元素 154:
  标签: div
  文本: 获取验证码
  类: ['com-relative', 'com-margin-b-15', 'com-height-30']
  ID: 
  父元素: form
  子元素数量: 2
  属性: {'class': ['com-relative', 'com-margin-b-15', 'com-height-30']}

元素 155:
  标签: input
  文本: 
  类: ['itxt02', 'com-float-left', 'com-font-size-14']
  ID: phone_authcode
  父元素: div
  子元素数量: 0
  属性: {'id': 'phone_authcode', 'type': 'text', 'class': ['itxt02', 'com-float-left', 'com-font-size-14'], 'name': 'phone_authcode', 'tabindex': '2', 'style': 'width:200px'}

元素 156:
  标签: a
  文本: 获取验证码
  类: ['com-color-666', 'com-font-size-14']
  ID: refresh_mobile_vcode
  父元素: div
  子元素数量: 0
  属性: {'href': 'javascript:void(0)', 'id': 'refresh_mobile_vcode', 'class': ['com-color-666', 'com-font-size-14']}

元素 157:
  标签: div
  文本: 忘记密码
  类: ['com-relative', 'com-z-index-1', 'com-margin-b-15', 'com-he-linhe-20']
  ID: 
  父元素: form
  子元素数量: 2
  属性: {'class': ['com-relative', 'com-z-index-1', 'com-margin-b-15', 'com-he-linhe-20']}

元素 158:
  标签: div
  文本: 忘记密码
  类: ['layui-nav-right']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['layui-nav-right']}

元素 159:
  标签: a
  文本: 忘记密码
  类: ['com-color-666']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://forgot.k3.cn', 'class': ['com-color-666'], 'target': '_blank'}

元素 160:
  标签: div
  文本: 
  类: ['com-relative', 'com-z-index-1', 'com-margin-b-10']
  ID: 
  父元素: form
  子元素数量: 1
  属性: {'class': ['com-relative', 'com-z-index-1', 'com-margin-b-10']}

元素 161:
  标签: input
  文本: 
  类: []
  ID: mobile_login
  父元素: div
  子元素数量: 0
  属性: {'type': 'submit', 'id': 'mobile_login', 'lay-submit': '', 'value': '登\xa0\xa0\xa0\xa0录'}

元素 162:
  标签: form
  文本: 用其他账号登录
  类: ['faster_login', 'hidden']
  ID: 
  父元素: div
  子元素数量: 3
  属性: {'class': ['faster_login', 'hidden'], 'method': 'post'}

元素 163:
  标签: input
  文本: 
  类: ['submit', 'fast_login']
  ID: 
  父元素: form
  子元素数量: 0
  属性: {'type': 'submit', 'value': '', 'class': ['submit', 'fast_login']}

元素 164:
  标签: div
  文本: 用其他账号登录
  类: ['other_login']
  ID: 
  父元素: form
  子元素数量: 1
  属性: {'class': ['other_login']}

元素 165:
  标签: a
  文本: 用其他账号登录
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': '#'}

元素 166:
  标签: div
  文本: QQ|微信立即注册
  类: ['com-pdl-pdr-20', 'third-party']
  ID: 
  父元素: div
  子元素数量: 13
  属性: {'class': ['com-pdl-pdr-20', 'third-party']}

元素 167:
  标签: ul
  文本: QQ|微信立即注册
  类: []
  ID: 
  父元素: div
  子元素数量: 12
  属性: {}

元素 168:
  标签: li
  文本: QQ|
  类: ['com-float-left']
  ID: 
  父元素: ul
  子元素数量: 4
  属性: {'class': ['com-float-left']}

元素 169:
  标签: a
  文本: QQ
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  父元素: li
  子元素数量: 2
  属性: {'href': '/auth/qq', 'class': ['pdl', 'com-inline-block', 'com-relative']}

元素 170:
  标签: b
  文本: 
  类: ['com-QQ-icon']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-QQ-icon']}

元素 171:
  标签: span
  文本: QQ
  类: ['com-font-size-12', 'com-color-666']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-font-size-12', 'com-color-666']}

元素 172:
  标签: span
  文本: |
  类: ['com-line']
  ID: 
  父元素: li
  子元素数量: 0
  属性: {'class': ['com-line']}

元素 173:
  标签: li
  文本: 微信
  类: ['com-float-left']
  ID: 
  父元素: ul
  子元素数量: 3
  属性: {'class': ['com-float-left']}

元素 174:
  标签: a
  文本: 微信
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  父元素: li
  子元素数量: 2
  属性: {'href': '/auth/wechat', 'class': ['pdl', 'com-inline-block', 'com-relative']}

元素 175:
  标签: b
  文本: 
  类: ['com-weixin-icon']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-weixin-icon']}

元素 176:
  标签: span
  文本: 微信
  类: ['com-font-size-12', 'com-color-666']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-font-size-12', 'com-color-666']}

元素 177:
  标签: li
  文本: 立即注册
  类: ['com-float-right']
  ID: 
  父元素: ul
  子元素数量: 2
  属性: {'class': ['com-float-right']}

元素 178:
  标签: a
  文本: 立即注册
  类: ['com-color-red', 'com-font-size-14']
  ID: 
  父元素: li
  子元素数量: 1
  属性: {'class': ['com-color-red', 'com-font-size-14'], 'href': 'http://reg.k3.cn', 'target': '_blank'}

元素 179:
  标签: b
  文本: 
  类: ['regist-link']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['regist-link']}

元素 180:
  标签: div
  文本: 公共场所不建议自动登录，以防账号丢失大小写锁定已打开看不清换一张忘记密码用其他账号登录QQ|微信立即注册
  类: ['layui-tab-item', 'layui-show', 'account-login']
  ID: 
  父元素: div
  子元素数量: 41
  属性: {'class': ['layui-tab-item', 'layui-show', 'account-login']}

元素 181:
  标签: div
  文本: 公共场所不建议自动登录，以防账号丢失大小写锁定已打开看不清换一张忘记密码用其他账号登录
  类: ['com-padding-20', 'com-relative', 'com-inline-block', 'account-box']
  ID: 
  父元素: div
  子元素数量: 26
  属性: {'class': ['com-padding-20', 'com-relative', 'com-inline-block', 'account-box']}

元素 182:
  标签: div
  文本: 公共场所不建议自动登录，以防账号丢失
  类: ['msg-wrap']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['msg-wrap']}

元素 183:
  标签: div
  文本: 公共场所不建议自动登录，以防账号丢失
  类: ['msg-error', 'com-hide']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['msg-error', 'com-hide']}

元素 184:
  标签: form
  文本: 大小写锁定已打开看不清换一张忘记密码
  类: ['layui-form', 'layui-form-pane', 'formlogin', '']
  ID: 
  父元素: div
  子元素数量: 19
  属性: {'class': ['layui-form', 'layui-form-pane', 'formlogin', '']}

元素 185:
  标签: div
  文本: 
  类: ['com-relative', 'com-z-index-1', 'com-margin-b-20', 'layui-user-login']
  ID: 
  父元素: form
  子元素数量: 3
  属性: {'class': ['com-relative', 'com-z-index-1', 'com-margin-b-20', 'layui-user-login']}

元素 186:
  标签: label
  文本: 
  类: ['layui-login-label', 'layui-name-label', 'lauyui-nav-left']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'for': 'loginname', 'class': ['layui-login-label', 'layui-name-label', 'lauyui-nav-left']}

元素 187:
  标签: input
  文本: 
  类: ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14']
  ID: loginname
  父元素: div
  子元素数量: 0
  属性: {'id': 'loginname', 'title': '请填写用户名/已验证手机', 'type': 'text', 'class': ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14'], 'name': 'loginname', 'placeholder': '用户名/已验证手机', 'required': '', 'value': '', 'tabindex': '1'}

元素 188:
  标签: span
  文本: 
  类: ['clear-btn']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['clear-btn'], 'onclick': '$("#loginname").val("")'}

元素 189:
  标签: div
  文本: 大小写锁定已打开
  类: ['com-relative', 'com-z-index-1', 'com-margin-b-20', 'layui-user-login']
  ID: 
  父元素: form
  子元素数量: 5
  属性: {'class': ['com-relative', 'com-z-index-1', 'com-margin-b-20', 'layui-user-login']}

元素 190:
  标签: label
  文本: 
  类: ['layui-login-label', 'layui-pwd-label', 'lauyui-nav-left']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['layui-login-label', 'layui-pwd-label', 'lauyui-nav-left'], 'for': 'nloginpwd'}

元素 191:
  标签: input
  文本: 
  类: ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14']
  ID: nloginpwd
  父元素: div
  子元素数量: 0
  属性: {'type': 'password', 'title': '请填写密码', 'id': 'nloginpwd', 'name': 'nloginpwd', 'class': ['com-input', 'com-pdl-10', 'com-float-right', 'com-font-size-14'], 'placeholder': '密码', 'required': '', 'value': '', 'tabindex': '2'}

元素 192:
  标签: span
  文本: 
  类: ['invisible']
  ID: plaintext
  父元素: div
  子元素数量: 0
  属性: {'id': 'plaintext', 'class': ['invisible']}

元素 193:
  标签: span
  文本: 大小写锁定已打开
  类: ['capslock']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['capslock'], 'style': 'display:none'}

元素 194:
  标签: b
  文本: 
  类: []
  ID: 
  父元素: span
  子元素数量: 0
  属性: {}

元素 195:
  标签: div
  文本: 看不清换一张
  类: ['com-relative', 'com-margin-b-15', 'com-height-30']
  ID: 
  父元素: form
  子元素数量: 3
  属性: {'class': ['com-relative', 'com-margin-b-15', 'com-height-30']}

元素 196:
  标签: input
  文本: 
  类: ['itxt02', 'com-float-left', 'com-margin-r-10', 'com-font-size-14']
  ID: authcode
  父元素: div
  子元素数量: 0
  属性: {'id': 'authcode', 'type': 'text', 'class': ['itxt02', 'com-float-left', 'com-margin-r-10', 'com-font-size-14'], 'maxlength': '4', 'name': 'authcode', 'tabindex': '3'}

元素 197:
  标签: div
  文本: 
  类: ['verify-code', 'com-float-left']
  ID: verify_code
  父元素: div
  子元素数量: 0
  属性: {'id': 'verify_code', 'class': ['verify-code', 'com-float-left']}

元素 198:
  标签: a
  文本: 看不清换一张
  类: ['com-float-right', 'com-color-666', 'com-font-size-12', 'com-width-40']
  ID: refresh_vcode
  父元素: div
  子元素数量: 0
  属性: {'href': 'javascript:void(0)', 'id': 'refresh_vcode', 'class': ['com-float-right', 'com-color-666', 'com-font-size-12', 'com-width-40']}

元素 199:
  标签: div
  文本: 忘记密码
  类: ['com-relative', 'com-z-index-1', 'com-margin-b-15', 'com-he-linhe-20']
  ID: 
  父元素: form
  子元素数量: 2
  属性: {'class': ['com-relative', 'com-z-index-1', 'com-margin-b-15', 'com-he-linhe-20']}

元素 200:
  标签: div
  文本: 忘记密码
  类: ['layui-nav-right']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['layui-nav-right']}

元素 201:
  标签: a
  文本: 忘记密码
  类: ['com-color-666']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://forgot.k3.cn', 'class': ['com-color-666'], 'target': '_blank'}

元素 202:
  标签: div
  文本: 
  类: ['com-relative', 'com-z-index-1', 'com-margin-b-10']
  ID: 
  父元素: form
  子元素数量: 1
  属性: {'class': ['com-relative', 'com-z-index-1', 'com-margin-b-10']}

元素 203:
  标签: input
  文本: 
  类: []
  ID: form_login
  父元素: div
  子元素数量: 0
  属性: {'type': 'submit', 'id': 'form_login', 'lay-submit': '', 'value': '登\xa0\xa0\xa0\xa0录'}

元素 204:
  标签: form
  文本: 用其他账号登录
  类: ['faster_login', 'hidden']
  ID: 
  父元素: div
  子元素数量: 3
  属性: {'class': ['faster_login', 'hidden'], 'method': 'post'}

元素 205:
  标签: input
  文本: 
  类: ['submit', 'fast_login']
  ID: 
  父元素: form
  子元素数量: 0
  属性: {'type': 'submit', 'value': '', 'class': ['submit', 'fast_login']}

元素 206:
  标签: div
  文本: 用其他账号登录
  类: ['other_login']
  ID: 
  父元素: form
  子元素数量: 1
  属性: {'class': ['other_login']}

元素 207:
  标签: a
  文本: 用其他账号登录
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': '#'}

元素 208:
  标签: div
  文本: QQ|微信立即注册
  类: ['com-pdl-pdr-20', 'third-party']
  ID: 
  父元素: div
  子元素数量: 13
  属性: {'class': ['com-pdl-pdr-20', 'third-party']}

元素 209:
  标签: ul
  文本: QQ|微信立即注册
  类: []
  ID: 
  父元素: div
  子元素数量: 12
  属性: {}

元素 210:
  标签: li
  文本: QQ|
  类: ['com-float-left']
  ID: 
  父元素: ul
  子元素数量: 4
  属性: {'class': ['com-float-left']}

元素 211:
  标签: a
  文本: QQ
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  父元素: li
  子元素数量: 2
  属性: {'href': '/auth/qq', 'class': ['pdl', 'com-inline-block', 'com-relative']}

元素 212:
  标签: b
  文本: 
  类: ['com-QQ-icon']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-QQ-icon']}

元素 213:
  标签: span
  文本: QQ
  类: ['com-font-size-12', 'com-color-666']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-font-size-12', 'com-color-666']}

元素 214:
  标签: span
  文本: |
  类: ['com-line']
  ID: 
  父元素: li
  子元素数量: 0
  属性: {'class': ['com-line']}

元素 215:
  标签: li
  文本: 微信
  类: ['com-float-left']
  ID: 
  父元素: ul
  子元素数量: 3
  属性: {'class': ['com-float-left']}

元素 216:
  标签: a
  文本: 微信
  类: ['pdl', 'com-inline-block', 'com-relative']
  ID: 
  父元素: li
  子元素数量: 2
  属性: {'href': '/auth/wechat', 'class': ['pdl', 'com-inline-block', 'com-relative']}

元素 217:
  标签: b
  文本: 
  类: ['com-weixin-icon']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-weixin-icon']}

元素 218:
  标签: span
  文本: 微信
  类: ['com-font-size-12', 'com-color-666']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['com-font-size-12', 'com-color-666']}

元素 219:
  标签: li
  文本: 立即注册
  类: ['com-float-right']
  ID: 
  父元素: ul
  子元素数量: 2
  属性: {'class': ['com-float-right']}

元素 220:
  标签: a
  文本: 立即注册
  类: ['com-color-red', 'com-font-size-14']
  ID: 
  父元素: li
  子元素数量: 1
  属性: {'class': ['com-color-red', 'com-font-size-14'], 'href': 'http://reg.k3.cn', 'target': '_blank'}

元素 221:
  标签: b
  文本: 
  类: ['regist-link']
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'class': ['regist-link']}

元素 222:
  标签: div
  文本: 登录成功
  类: ['shade-box', 'hidden']
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'class': ['shade-box', 'hidden']}

元素 223:
  标签: div
  文本: 登录成功
  类: ['shade-content']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['shade-content']}

元素 224:
  标签: div
  文本: 检测到您的账号登录地址异常为了您的账号安全请通过本次验证135****2305获取验证码确定
  类: ['hidden']
  ID: securityVerifyTpl
  父元素: [document]
  子元素数量: 12
  属性: {'id': 'securityVerifyTpl', 'class': ['hidden']}

元素 225:
  标签: div
  文本: 检测到您的账号登录地址异常为了您的账号安全请通过本次验证135****2305获取验证码确定
  类: ['verifyBox']
  ID: 
  父元素: div
  子元素数量: 11
  属性: {'class': ['verifyBox']}

元素 226:
  标签: p
  文本: 检测到您的账号登录地址异常
  类: []
  ID: 
  父元素: div
  子元素数量: 1
  属性: {}

元素 227:
  标签: b
  文本: 检测到您的账号登录地址异常
  类: []
  ID: 
  父元素: p
  子元素数量: 0
  属性: {'style': 'font-size:18px;color:#000'}

元素 228:
  标签: p
  文本: 为了您的账号安全请通过本次验证
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'style': 'color:#777;margin:5px 0'}

元素 229:
  标签: div
  文本: 135****2305
  类: ['item']
  ID: 
  父元素: div
  子元素数量: 2
  属性: {'class': ['item']}

元素 230:
  标签: img
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'src': '/dist/img/logo/sjh.png?v=139a32d805'}

元素 231:
  标签: b
  文本: 135****2305
  类: []
  ID: userName
  父元素: div
  子元素数量: 0
  属性: {'id': 'userName', 'style': 'color:#000;font-size:16px'}

元素 232:
  标签: div
  文本: 获取验证码
  类: ['item', 'verifyCode']
  ID: 
  父元素: div
  子元素数量: 3
  属性: {'class': ['item', 'verifyCode']}

元素 233:
  标签: img
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'src': '/dist/img/logo/yzm.png?v=2c51e466cc'}

元素 234:
  标签: input
  文本: 
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'type': 'text', 'maxlength': '6', 'placeholder': '请输入验证码'}

元素 235:
  标签: span
  文本: 获取验证码
  类: ['key-code']
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'class': ['key-code']}

元素 236:
  标签: div
  文本: 确定
  类: ['notFilledIn']
  ID: submitVerifyCode
  父元素: div
  子元素数量: 0
  属性: {'class': ['notFilledIn'], 'id': 'submitVerifyCode'}

元素 237:
  标签: script
  文本: 
  类: []
  ID: 
  父元素: [document]
  子元素数量: 0
  属性: {'type': 'text/javascript', 'src': '//public.k3cdn.com/jquery.cookie/1.4.1/jquery.cookie.js'}

元素 238:
  标签: script
  文本: 
  类: []
  ID: 
  父元素: [document]
  子元素数量: 0
  属性: {'type': 'text/javascript', 'src': '//public.k3cdn.com/jquery.qrcode/jquery.qrcode.min.js'}

元素 239:
  标签: script
  文本: 
  类: []
  ID: 
  父元素: [document]
  子元素数量: 0
  属性: {'type': 'text/javascript', 'src': '//public.k3cdn.com/jquery.base64/1.0/jquery.base64.min.js'}

元素 240:
  标签: script
  文本: 
  类: []
  ID: J_secure_sdk_v2
  父元素: [document]
  子元素数量: 0
  属性: {'type': 'text/javascript', 'src': 'http://g.tbcdn.cn/sj/securesdk/0.0.3/securesdk_v2.js', 'id': 'J_secure_sdk_v2', 'data-appkey': '12622718'}

元素 241:
  标签: script
  文本: 
  类: []
  ID: 
  父元素: [document]
  子元素数量: 0
  属性: {'type': 'text/javascript', 'src': '/dist/js/user.login.js?v=a145715499'}

元素 242:
  标签: div
  文本: 平台介绍联系我们商业合作什么值得卖温岭女鞋温岭童鞋聚衣网福建男鞋白沟网供兴城泳装服务热线 4006266818©2014-2025 www.k3.cn（开山网）版权所有©2025 北京聚源百成网络科技有限公司京ICP备14053148号-1京公网安备 11010802030672号投诉电话：184 0178 7818
  类: ['footer']
  ID: 
  父元素: [document]
  子元素数量: 17
  属性: {'class': ['footer']}

元素 243:
  标签: div
  文本: 平台介绍联系我们商业合作什么值得卖温岭女鞋温岭童鞋聚衣网福建男鞋白沟网供兴城泳装服务热线 4006266818
  类: ['layui-breadcrumb', 'layui-top30', 'layui-text-center', 'layui-footer', 'footer-drainage']
  ID: 
  父元素: div
  子元素数量: 11
  属性: {'class': ['layui-breadcrumb', 'layui-top30', 'layui-text-center', 'layui-footer', 'footer-drainage'], 'lay-separator': '|'}

元素 244:
  标签: a
  文本: 平台介绍
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.k3.cn/ajax/message/about_us?type=platform', 'type': 'platform', 'target': '_blank'}

元素 245:
  标签: a
  文本: 联系我们
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.k3.cn/ajax/message/about_us?type=contacts', 'type': 'contacts', 'target': '_blank'}

元素 246:
  标签: a
  文本: 商业合作
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.k3.cn/ajax/message/about_us?type=business', 'type': 'business', 'target': '_blank'}

元素 247:
  标签: a
  文本: 什么值得卖
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://notice.k3.cn/pages/smzdm.html', 'target': '_blank'}

元素 248:
  标签: a
  文本: 温岭女鞋
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.k3.cn/', 'target': '_blank'}

元素 249:
  标签: a
  文本: 温岭童鞋
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.2tong.cn/', 'target': '_blank'}

元素 250:
  标签: a
  文本: 聚衣网
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.juyi5.cn/', 'target': '_blank'}

元素 251:
  标签: a
  文本: 福建男鞋
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.xingfujie.cn/', 'target': '_blank'}

元素 252:
  标签: a
  文本: 白沟网供
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.bao66.cn/', 'target': '_blank'}

元素 253:
  标签: a
  文本: 兴城泳装
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'http://www.yoduo.com/', 'target': '_blank'}

元素 254:
  标签: a
  文本: 服务热线 4006266818
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'href': 'javascript:;'}

元素 255:
  标签: div
  文本: ©2014-2025 www.k3.cn（开山网）版权所有©2025 北京聚源百成网络科技有限公司京ICP备14053148号-1京公网安备 11010802030672号投诉电话：184 0178 7818
  类: ['layui-breadcrumb', 'layui-text-center', 'layui-footer', 'copyright']
  ID: 
  父元素: div
  子元素数量: 4
  属性: {'class': ['layui-breadcrumb', 'layui-text-center', 'layui-footer', 'copyright']}

元素 256:
  标签: a
  文本: 版权所有©2025 北京聚源百成网络科技有限公司
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'target': '_blank', 'href': 'http://www.jybc.com.cn/', 'style': 'color:#666'}

元素 257:
  标签: a
  文本: 京ICP备14053148号-1
  类: []
  ID: 
  父元素: div
  子元素数量: 0
  属性: {'target': '_blank', 'href': 'http://beian.miit.gov.cn'}

元素 258:
  标签: a
  文本: 京公网安备 11010802030672号
  类: []
  ID: 
  父元素: div
  子元素数量: 1
  属性: {'target': '_blank', 'href': 'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802030672'}

元素 259:
  标签: img
  文本: 
  类: []
  ID: 
  父元素: a
  子元素数量: 0
  属性: {'src': 'http://misc.k3cdn.com/k/20191216/2019121614145277011767.png', 'style': 'vertical-align:sub'}

元素 260:
  标签: script
  文本: 
  类: []
  ID: 
  父元素: [document]
  子元素数量: 0
  属性: {'type': 'text/javascript', 'src': '/dist/js/footer.js?v=d55e243b05'}

