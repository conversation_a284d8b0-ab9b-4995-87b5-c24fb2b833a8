import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import urllib.parse
import json
import re

class K3WebScraperFinal:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None
    
    def extract_from_k3_main(self):
        """从K3主页提取信息"""
        url = "https://www.k3.cn/"
        response = self.get_page_content(url)
        
        if not response:
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        products = []
        
        # 提取主页上的链接和信息
        links = soup.find_all('a', href=True)
        
        for link in links:
            text = link.get_text(strip=True)
            href = link['href']
            
            # 过滤包含鞋类相关关键词的链接
            shoe_keywords = ['鞋', 'shoe', '运动', 'sport', '跑步', 'run']
            if any(keyword in text.lower() for keyword in shoe_keywords) and len(text) > 3:
                if href.startswith('/'):
                    href = 'https://www.k3.cn' + href
                
                products.append({
                    '标题': text,
                    '价格': '需要进一步查询',
                    '链接': href,
                    '图片': '需要进一步查询',
                    '描述': f'从K3主页提取的鞋类相关链接: {text}',
                    '来源': 'K3主页'
                })
        
        return products
    
    def extract_from_alternative_sources(self):
        """从其他来源提取运动鞋信息"""
        products = []
        
        # 示例：从一些公开的API或网站获取数据
        alternative_urls = [
            "https://httpbin.org/json",  # 测试JSON API
        ]
        
        for url in alternative_urls:
            try:
                response = self.get_page_content(url)
                if response:
                    # 尝试解析JSON
                    try:
                        data = response.json()
                        products.append({
                            '标题': '测试数据源',
                            '价格': '测试价格',
                            '链接': url,
                            '图片': '无',
                            '描述': f'从 {url} 获取的测试数据',
                            '来源': '测试API'
                        })
                    except:
                        pass
            except Exception as e:
                print(f"访问 {url} 失败: {e}")
        
        return products
    
    def create_sample_shoe_data(self):
        """创建示例运动鞋数据"""
        sample_data = [
            {
                '标题': 'Nike Air Force 1 经典白鞋',
                '价格': '¥799',
                '链接': 'https://www.nike.com/cn/air-force-1',
                '图片': 'https://example.com/nike-af1.jpg',
                '描述': 'Nike经典款式，百搭白色运动鞋，适合各种场合',
                '来源': '示例数据'
            },
            {
                '标题': 'Adidas Stan Smith 绿尾小白鞋',
                '价格': '¥699',
                '链接': 'https://www.adidas.com.cn/stan-smith',
                '图片': 'https://example.com/adidas-stan-smith.jpg',
                '描述': 'Adidas经典绿尾小白鞋，简约时尚设计',
                '来源': '示例数据'
            },
            {
                '标题': 'Converse All Star 高帮帆布鞋',
                '价格': '¥459',
                '链接': 'https://www.converse.com.cn/all-star',
                '图片': 'https://example.com/converse-allstar.jpg',
                '描述': 'Converse经典高帮帆布鞋，青春潮流必备',
                '来源': '示例数据'
            },
            {
                '标题': 'New Balance 574 复古跑鞋',
                '价格': '¥899',
                '链接': 'https://www.newbalance.com.cn/574',
                '图片': 'https://example.com/nb-574.jpg',
                '描述': 'New Balance经典574系列，复古设计舒适穿着',
                '来源': '示例数据'
            },
            {
                '标题': 'Vans Old Skool 滑板鞋',
                '价格': '¥599',
                '链接': 'https://www.vans.cn/old-skool',
                '图片': 'https://example.com/vans-oldskool.jpg',
                '描述': 'Vans经典滑板鞋，街头文化的象征',
                '来源': '示例数据'
            }
        ]
        return sample_data
    
    def save_to_excel(self, products, filename='运动鞋数据.xlsx'):
        """保存数据到Excel文件"""
        if not products:
            print("没有数据可保存")
            return False
        
        try:
            df = pd.DataFrame(products)
            
            # 按来源分组保存到不同的工作表
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 保存所有数据到主工作表
                df.to_excel(writer, sheet_name='所有数据', index=False)
                
                # 按来源分组保存
                sources = df['来源'].unique()
                for source in sources:
                    source_data = df[df['来源'] == source]
                    sheet_name = source[:30]  # Excel工作表名称限制
                    source_data.to_excel(writer, sheet_name=sheet_name, index=False)
            
            print(f"数据已保存到 {filename}")
            print(f"共保存了 {len(products)} 条记录")
            print(f"包含 {len(sources)} 个数据源")
            return True
        except Exception as e:
            print(f"保存Excel文件时出错: {e}")
            return False
    
    def run_comprehensive_scraping(self):
        """运行综合抓取"""
        all_products = []
        
        print("=== 开始综合数据抓取 ===\n")
        
        # 1. 尝试从K3主页提取
        print("1. 尝试从K3主页提取数据...")
        k3_products = self.extract_from_k3_main()
        if k3_products:
            all_products.extend(k3_products)
            print(f"从K3主页提取到 {len(k3_products)} 条数据")
        else:
            print("从K3主页未能提取到数据")
        
        # 2. 尝试其他数据源
        print("\n2. 尝试其他数据源...")
        alt_products = self.extract_from_alternative_sources()
        if alt_products:
            all_products.extend(alt_products)
            print(f"从其他源提取到 {len(alt_products)} 条数据")
        
        # 3. 添加示例数据
        print("\n3. 添加示例运动鞋数据...")
        sample_products = self.create_sample_shoe_data()
        all_products.extend(sample_products)
        print(f"添加了 {len(sample_products)} 条示例数据")
        
        return all_products

def main():
    """主函数"""
    print("=== K3网站运动鞋数据抓取工具 ===\n")
    
    scraper = K3WebScraperFinal()
    
    # 运行综合抓取
    products = scraper.run_comprehensive_scraping()
    
    if products:
        print(f"\n=== 抓取完成 ===")
        print(f"总共获取了 {len(products)} 条数据")
        
        # 显示数据预览
        print(f"\n数据预览 (前3条):")
        for i, product in enumerate(products[:3]):
            print(f"\n第 {i+1} 条:")
            for key, value in product.items():
                print(f"  {key}: {value}")
        
        # 保存到Excel
        print(f"\n正在保存数据到Excel...")
        success = scraper.save_to_excel(products, '运动鞋数据_完整版.xlsx')
        
        if success:
            print(f"\n✅ 数据已成功保存到 '运动鞋数据_完整版.xlsx'")
            print(f"📊 文件包含多个工作表，按数据来源分类")
        else:
            print(f"\n❌ 保存失败")
    else:
        print(f"\n未能获取到任何数据")

if __name__ == "__main__":
    main()
