import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import urllib.parse
import json
import re
import os
from urllib.parse import urljoin, urlparse
import base64

class K3WebScraperFinal:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
        self.images_folder = 'downloaded_images'
        self.create_images_folder()

    def create_images_folder(self):
        """创建图片下载文件夹"""
        if not os.path.exists(self.images_folder):
            os.makedirs(self.images_folder)
            print(f"创建图片文件夹: {self.images_folder}")

    def download_image(self, image_url, filename):
        """下载图片"""
        try:
            if not image_url or image_url == '无' or 'example.com' in image_url:
                return None

            # 处理相对URL
            if image_url.startswith('//'):
                image_url = 'https:' + image_url
            elif image_url.startswith('/'):
                image_url = 'https://www.k3.cn' + image_url

            print(f"正在下载图片: {image_url}")

            response = self.session.get(image_url, timeout=10, stream=True)
            response.raise_for_status()

            # 检查是否是图片
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                print(f"URL不是图片: {content_type}")
                return None

            # 确定文件扩展名
            if 'jpeg' in content_type or 'jpg' in content_type:
                ext = '.jpg'
            elif 'png' in content_type:
                ext = '.png'
            elif 'gif' in content_type:
                ext = '.gif'
            elif 'webp' in content_type:
                ext = '.webp'
            else:
                ext = '.jpg'  # 默认

            # 生成文件名
            safe_filename = re.sub(r'[^\w\-_.]', '_', filename)
            if not safe_filename.endswith(ext):
                safe_filename += ext

            filepath = os.path.join(self.images_folder, safe_filename)

            # 保存图片
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            print(f"图片已保存: {filepath}")
            return filepath

        except Exception as e:
            print(f"下载图片失败 {image_url}: {e}")
            return None

    def extract_images_from_page(self, url, max_images=5):
        """从页面提取图片"""
        response = self.get_page_content(url)
        if not response:
            return []

        soup = BeautifulSoup(response.content, 'html.parser')
        images = []

        # 查找所有图片标签
        img_tags = soup.find_all('img', src=True)

        for i, img in enumerate(img_tags[:max_images]):
            src = img.get('src')
            alt = img.get('alt', f'image_{i+1}')

            # 过滤掉小图标和无关图片
            if any(skip in src.lower() for skip in ['icon', 'logo', 'avatar', 'btn', 'button']):
                continue

            # 构建完整URL
            if src.startswith('//'):
                src = 'https:' + src
            elif src.startswith('/'):
                src = urljoin(url, src)
            elif not src.startswith('http'):
                src = urljoin(url, src)

            images.append({
                'url': src,
                'alt': alt,
                'title': img.get('title', ''),
            })

        return images
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None
    
    def extract_from_k3_main(self):
        """从K3主页提取信息"""
        url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
        response = self.get_page_content(url)

        if not response:
            return []

        soup = BeautifulSoup(response.content, 'html.parser')
        products = []

        # 首先提取页面上的所有图片
        print("正在提取K3主页的图片...")
        page_images = self.extract_images_from_page(url, max_images=10)

        # 提取主页上的链接和信息
        links = soup.find_all('a', href=True)

        for i, link in enumerate(links):
            text = link.get_text(strip=True)
            href = link['href']

            # 过滤包含鞋类相关关键词的链接
            shoe_keywords = ['鞋', 'shoe', '运动', 'sport', '跑步', 'run']
            if any(keyword in text.lower() for keyword in shoe_keywords) and len(text) > 3:
                if href.startswith('/'):
                    href = 'https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html' + href

                # 查找链接附近的图片
                img_in_link = link.find('img', src=True)
                image_url = '需要进一步查询'
                local_image_path = '无'

                if img_in_link:
                    img_src = img_in_link['src']
                    if img_src.startswith('//'):
                        img_src = 'https:' + img_src
                    elif img_src.startswith('/'):
                        img_src = 'https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html' + img_src

                    image_url = img_src

                    # 下载图片
                    safe_text = re.sub(r'[^\w\-_.]', '_', text[:20])
                    filename = f"k3_shoe_{i+1}_{safe_text}"
                    local_path = self.download_image(img_src, filename)
                    if local_path:
                        local_image_path = local_path

                products.append({
                    '标题': text,
                    '价格': '需要进一步查询',
                    '链接': href,
                    '图片URL': image_url,
                    '本地图片路径': local_image_path,
                    '描述': f'从K3主页提取的鞋类相关链接: {text}',
                    '来源': 'K3主页'
                })
    
    def save_to_excel(self, products, filename='运动鞋数据.xlsx'):
        """保存数据到Excel文件"""
        if not products:
            print("没有数据可保存")
            return False

        try:
            df = pd.DataFrame(products)

            # 统计下载的图片数量
            downloaded_images = 0
            if '本地图片路径' in df.columns:
                downloaded_images = len(df[df['本地图片路径'].notna() &
                                       (df['本地图片路径'] != '无') &
                                       (df['本地图片路径'] != '下载失败')])

            # 按来源分组保存到不同的工作表
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 保存所有数据到主工作表
                df.to_excel(writer, sheet_name='所有数据', index=False)

                # 按来源分组保存
                sources = df['来源'].unique()
                for source in sources:
                    source_data = df[df['来源'] == source]
                    sheet_name = source[:30]  # Excel工作表名称限制
                    source_data.to_excel(writer, sheet_name=sheet_name, index=False)

                # 创建图片统计工作表
                if downloaded_images > 0:
                    image_stats = []
                    for _, row in df.iterrows():
                        if (row.get('本地图片路径') and
                            row['本地图片路径'] not in ['无', '下载失败']):
                            image_stats.append({
                                '产品标题': row['标题'],
                                '图片URL': row.get('图片URL', ''),
                                '本地路径': row['本地图片路径'],
                                '文件大小': self.get_file_size(row['本地图片路径'])
                            })

                    if image_stats:
                        img_df = pd.DataFrame(image_stats)
                        img_df.to_excel(writer, sheet_name='下载的图片', index=False)

            print(f"数据已保存到 {filename}")
            print(f"共保存了 {len(products)} 条记录")
            print(f"包含 {len(sources)} 个数据源")
            print(f"成功下载了 {downloaded_images} 张图片")
            return True
        except Exception as e:
            print(f"保存Excel文件时出错: {e}")
            return False

    def get_file_size(self, filepath):
        """获取文件大小"""
        try:
            if filepath and os.path.exists(filepath):
                size = os.path.getsize(filepath)
                if size < 1024:
                    return f"{size} B"
                elif size < 1024 * 1024:
                    return f"{size/1024:.1f} KB"
                else:
                    return f"{size/(1024*1024):.1f} MB"
            return "未知"
        except:
            return "未知"
    
    def run_comprehensive_scraping(self):
        """运行综合抓取"""
        all_products = []
        
        print("=== 开始综合数据抓取 ===\n")
        
        # 1. 尝试从K3主页提取
        print("1. 尝试从K3主页提取数据...")
        k3_products = self.extract_from_k3_main()
        if k3_products:
            all_products.extend(k3_products)
            print(f"从K3主页提取到 {len(k3_products)} 条数据")
        else:
            print("从K3主页未能提取到数据")
        
        return all_products

def main():
    """主函数"""
    print("=== K3网站运动鞋数据抓取工具 ===\n")
    
    scraper = K3WebScraperFinal()
    
    # 运行综合抓取
    products = scraper.run_comprehensive_scraping()
    
    if products:
        print(f"\n=== 抓取完成 ===")
        print(f"总共获取了 {len(products)} 条数据")
        
        # 显示数据预览
        print(f"\n数据预览 (前3条):")
        for i, product in enumerate(products[:3]):
            print(f"\n第 {i+1} 条:")
            for key, value in product.items():
                print(f"  {key}: {value}")
        
        # 保存到Excel
        print(f"\n正在保存数据到Excel...")
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'运动鞋数据_带图片_{timestamp}.xlsx'
        success = scraper.save_to_excel(products, filename)
        
        if success:
            print(f"\n✅ 数据已成功保存到 '{filename}'")
            print(f"📊 文件包含多个工作表，按数据来源分类")
            print(f"🖼️ 图片已下载到 'downloaded_images' 文件夹")
        else:
            print(f"\n❌ 保存失败")
    else:
        print(f"\n未能获取到任何数据")

if __name__ == "__main__":
    main()
