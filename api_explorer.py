import requests
import json
import time
import pandas as pd
from urllib.parse import urljoin, urlparse, parse_qs, unquote
import base64

class K3ApiExplorer:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.k3.cn/',
            'X-Requested-With': 'XMLHttpRequest'
        }
        self.session.headers.update(self.headers)
        self.base_url = 'https://www.k3.cn'
    
    def explore_api_endpoints(self):
        """探索API端点"""
        # 从之前的分析中发现的API端点
        api_endpoints = [
            '/api/chat/service',
            '/api/jump/manage',
            '/api/jump/manage/taobaoPublish',
            '/api/jump/product',
            '/search/api',  # 推测的搜索API
            '/api/search',  # 推测的搜索API
            '/api/product/list',  # 推测的产品列表API
            '/api/data/search'  # 推测的数据搜索API
        ]
        
        results = []
        
        for endpoint in api_endpoints:
            print(f"\n正在探索API端点: {endpoint}")
            result = self.test_api_endpoint(endpoint)
            results.append(result)
            time.sleep(1)  # 避免请求过快
        
        return results
    
    def test_api_endpoint(self, endpoint):
        """测试API端点"""
        full_url = urljoin(self.base_url, endpoint)
        
        result = {
            'endpoint': endpoint,
            'full_url': full_url,
            'methods_tested': [],
            'responses': {}
        }
        
        # 测试不同的HTTP方法
        methods = ['GET', 'POST']
        
        for method in methods:
            try:
                print(f"  测试 {method} 方法...")
                
                if method == 'GET':
                    response = self.session.get(full_url, timeout=10)
                else:
                    # POST请求尝试不同的数据格式
                    test_data = {'keyword': '运动鞋', 'page': 1, 'limit': 10}
                    response = self.session.post(full_url, json=test_data, timeout=10)
                
                result['methods_tested'].append(method)
                
                response_info = {
                    'status_code': response.status_code,
                    'headers': dict(response.headers),
                    'content_type': response.headers.get('content-type', ''),
                    'content_length': len(response.content),
                    'content_preview': ''
                }
                
                # 尝试解析响应内容
                try:
                    if 'application/json' in response_info['content_type']:
                        json_data = response.json()
                        response_info['content_preview'] = json.dumps(json_data, ensure_ascii=False, indent=2)[:500]
                        response_info['is_json'] = True
                    else:
                        response_info['content_preview'] = response.text[:500]
                        response_info['is_json'] = False
                except:
                    response_info['content_preview'] = str(response.content[:500])
                    response_info['is_json'] = False
                
                result['responses'][method] = response_info
                
                print(f"    状态码: {response.status_code}")
                print(f"    内容类型: {response_info['content_type']}")
                
            except Exception as e:
                print(f"    {method} 请求失败: {e}")
                result['responses'][method] = {'error': str(e)}
        
        return result
    
    def try_search_apis(self):
        """尝试搜索相关的API"""
        print(f"\n=== 尝试搜索API ===")
        
        # 可能的搜索API路径
        search_paths = [
            '/search/api/web',
            '/api/search/web',
            '/api/search/product',
            '/search/product/api',
            '/api/v1/search',
            '/api/search',
            '/search/ajax'
        ]
        
        search_params = {
            'keyword': '运动鞋',
            'q': '运动鞋',
            'search': '运动鞋',
            'type': 'all',
            'page': 1,
            'limit': 10,
            'category': 'all'
        }
        
        results = []
        
        for path in search_paths:
            print(f"\n尝试搜索API: {path}")
            
            # GET请求带参数
            try:
                url = urljoin(self.base_url, path)
                response = self.session.get(url, params=search_params, timeout=10)
                
                result = {
                    'path': path,
                    'method': 'GET',
                    'params': search_params,
                    'status_code': response.status_code,
                    'content_type': response.headers.get('content-type', ''),
                    'content_length': len(response.content)
                }
                
                if response.status_code == 200:
                    try:
                        if 'json' in result['content_type']:
                            data = response.json()
                            result['response_data'] = data
                            print(f"  成功! 返回JSON数据: {len(str(data))} 字符")
                        else:
                            result['response_text'] = response.text[:200]
                            print(f"  返回HTML/文本: {len(response.text)} 字符")
                    except:
                        result['response_text'] = response.text[:200]
                
                results.append(result)
                
            except Exception as e:
                print(f"  请求失败: {e}")
        
        return results
    
    def analyze_page_network_requests(self):
        """分析页面可能的网络请求"""
        print(f"\n=== 分析页面网络请求 ===")
        
        # 基于页面URL结构推测可能的API
        original_url = "https://www.k3.cn/search/web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2.html"
        
        # 解析URL参数
        # web,all,%E8%BF%90%E5%8A%A8%E9%9E%8B,,1,2
        # 可能的格式: type,category,keyword,filter,page,pagesize
        
        url_parts = original_url.split('/')[-1].replace('.html', '').split(',')
        print(f"URL参数分析: {url_parts}")
        
        if len(url_parts) >= 3:
            search_type = url_parts[0]  # web
            category = url_parts[1]     # all
            keyword = unquote(url_parts[2])  # 运动鞋
            page = url_parts[-2] if len(url_parts) > 4 else '1'  # 1
            pagesize = url_parts[-1] if len(url_parts) > 5 else '2'  # 2
            
            print(f"  搜索类型: {search_type}")
            print(f"  分类: {category}")
            print(f"  关键词: {keyword}")
            print(f"  页码: {page}")
            print(f"  页面大小: {pagesize}")
            
            # 尝试构造API请求
            api_attempts = [
                f"/search/api/{search_type},{category},{url_parts[2]},,{page},{pagesize}",
                f"/api/search/{search_type}/{category}",
                f"/search/ajax",
                f"/api/search"
            ]
            
            for api_url in api_attempts:
                try:
                    print(f"\n尝试API: {api_url}")
                    full_url = urljoin(self.base_url, api_url)
                    
                    # GET请求
                    response = self.session.get(full_url, timeout=10)
                    print(f"  GET {response.status_code}: {response.headers.get('content-type', '')}")
                    
                    if response.status_code == 200 and 'json' in response.headers.get('content-type', ''):
                        try:
                            data = response.json()
                            print(f"  成功获取JSON数据!")
                            return {'api_url': api_url, 'data': data}
                        except:
                            pass
                    
                    # POST请求
                    post_data = {
                        'type': search_type,
                        'category': category,
                        'keyword': keyword,
                        'page': int(page),
                        'pagesize': int(pagesize)
                    }
                    
                    response = self.session.post(full_url, json=post_data, timeout=10)
                    print(f"  POST {response.status_code}: {response.headers.get('content-type', '')}")
                    
                    if response.status_code == 200 and 'json' in response.headers.get('content-type', ''):
                        try:
                            data = response.json()
                            print(f"  成功获取JSON数据!")
                            return {'api_url': api_url, 'data': data, 'method': 'POST'}
                        except:
                            pass
                            
                except Exception as e:
                    print(f"  请求失败: {e}")
        
        return None
    
    def save_results(self, api_results, search_results, network_analysis):
        """保存探索结果"""
        all_results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'api_exploration': api_results,
            'search_api_attempts': search_results,
            'network_analysis': network_analysis
        }
        
        filename = f'k3_api_exploration_{int(time.time())}.json'
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"\nAPI探索结果已保存到: {filename}")
        
        # 创建Excel报告
        self.create_excel_report(all_results)
        
        return filename
    
    def create_excel_report(self, results):
        """创建Excel报告"""
        filename = f'k3_api_report_{int(time.time())}.xlsx'
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # API端点测试结果
            api_data = []
            for result in results['api_exploration']:
                for method, response in result['responses'].items():
                    if 'error' not in response:
                        api_data.append({
                            'API端点': result['endpoint'],
                            'HTTP方法': method,
                            '状态码': response['status_code'],
                            '内容类型': response['content_type'],
                            '内容长度': response['content_length'],
                            '是否JSON': response.get('is_json', False)
                        })
            
            if api_data:
                api_df = pd.DataFrame(api_data)
                api_df.to_excel(writer, sheet_name='API测试结果', index=False)
            
            # 搜索API尝试结果
            search_data = []
            for result in results['search_api_attempts']:
                search_data.append({
                    'API路径': result['path'],
                    'HTTP方法': result['method'],
                    '状态码': result['status_code'],
                    '内容类型': result['content_type'],
                    '内容长度': result['content_length'],
                    '是否成功': result['status_code'] == 200
                })
            
            if search_data:
                search_df = pd.DataFrame(search_data)
                search_df.to_excel(writer, sheet_name='搜索API测试', index=False)
        
        print(f"Excel报告已保存到: {filename}")

def main():
    """主函数"""
    print("=== K3网站API探索工具 ===\n")
    
    explorer = K3ApiExplorer()
    
    # 1. 探索已知的API端点
    print("1. 探索已知API端点...")
    api_results = explorer.explore_api_endpoints()
    
    # 2. 尝试搜索相关API
    print("\n2. 尝试搜索API...")
    search_results = explorer.try_search_apis()
    
    # 3. 分析页面网络请求
    print("\n3. 分析页面网络请求...")
    network_analysis = explorer.analyze_page_network_requests()
    
    # 4. 保存结果
    print("\n4. 保存探索结果...")
    result_file = explorer.save_results(api_results, search_results, network_analysis)
    
    print(f"\n=== API探索完成 ===")
    print(f"结果文件: {result_file}")

if __name__ == "__main__":
    main()
