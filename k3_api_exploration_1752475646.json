{"timestamp": "2025-07-14 14:47:26", "api_exploration": [{"endpoint": "/api/chat/service", "full_url": "https://www.k3.cn/api/chat/service", "methods_tested": ["GET", "POST"], "responses": {"GET": {"status_code": 200, "headers": {"Date": "Mon, 14 Jul 2025 06:47:16 GMT", "Content-Type": "text/html; charset=utf-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Set-Cookie": "tgw_l7_route=8832461349e7ce9775164f37bbb5b258; Expires=Mon, 14-Jul-2025 07:17:16 GMT; Path=/; sameSite=None; Secure", "Server": "openresty", "Vary": "Accept-Encoding", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=utf-8", "content_length": 29165, "content_preview": "<!DOCTYPE html>\n<html style=\"height: 100%;\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, user-scalable=no,initial-scale=1,minimum-scale=1,maximum-scale=1\">\n    <title></title>\n    <style>\n*{padding:0;margin:0}.hidden{display:none!important}body{height:100%}.container .icon{display:inline-block}.container p{margin:0 auto}.container .button{display:block;text-decoration:none;margin:0 auto}.conta", "is_json": false}, "POST": {"status_code": 200, "headers": {"Date": "Mon, 14 Jul 2025 06:47:16 GMT", "Content-Type": "text/html; charset=utf-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "openresty", "Vary": "Accept-Encoding", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=utf-8", "content_length": 29165, "content_preview": "<!DOCTYPE html>\n<html style=\"height: 100%;\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, user-scalable=no,initial-scale=1,minimum-scale=1,maximum-scale=1\">\n    <title></title>\n    <style>\n*{padding:0;margin:0}.hidden{display:none!important}body{height:100%}.container .icon{display:inline-block}.container p{margin:0 auto}.container .button{display:block;text-decoration:none;margin:0 auto}.conta", "is_json": false}}}, {"endpoint": "/api/jump/manage", "full_url": "https://www.k3.cn/api/jump/manage", "methods_tested": ["GET", "POST"], "responses": {"GET": {"status_code": 200, "headers": {"Connection": "close", "Transfer-Encoding": "chunked", "Cache-Control": "no-cache,must-revalidate", "Content-Type": "text/html; charset=UTF-8", "Date": "Mon, 14 Jul 2025 06:47:17 GMT", "Pragma": "no-cache", "Server": "JYBC WEB/2.0"}, "content_type": "text/html; charset=UTF-8", "content_length": 1038, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<title>很抱歉</title>\n<meta charset=\"utf-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<style type=\"text/css\">\n    *{ margin:0;padding:0}\n    body { background: #F2F2F2; }\n    .container{padding: 250px 0;text-align: center;}\n</style>\n</head>\n\n<body>\n    <div class=\"container\">\n        <div><img src=\"/images/wuliao/load_page.png\"/></div>\n        <p style=\"font-size:18px\">请重新登录</p>\n        <p style=\"color:#666\">详情请咨询客服电话/QQ：40062668", "is_json": false}, "POST": {"status_code": 200, "headers": {"Connection": "close", "Transfer-Encoding": "chunked", "Cache-Control": "no-cache,must-revalidate", "Content-Type": "text/html; charset=UTF-8", "Date": "Mon, 14 Jul 2025 06:47:17 GMT", "Pragma": "no-cache", "Server": "JYBC WEB/2.0"}, "content_type": "text/html; charset=UTF-8", "content_length": 1038, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<title>很抱歉</title>\n<meta charset=\"utf-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<style type=\"text/css\">\n    *{ margin:0;padding:0}\n    body { background: #F2F2F2; }\n    .container{padding: 250px 0;text-align: center;}\n</style>\n</head>\n\n<body>\n    <div class=\"container\">\n        <div><img src=\"/images/wuliao/load_page.png\"/></div>\n        <p style=\"font-size:18px\">请重新登录</p>\n        <p style=\"color:#666\">详情请咨询客服电话/QQ：40062668", "is_json": false}}}, {"endpoint": "/api/jump/manage/taobaoPublish", "full_url": "https://www.k3.cn/api/jump/manage/taobaoPublish", "methods_tested": ["GET", "POST"], "responses": {"GET": {"status_code": 200, "headers": {"Connection": "close", "Transfer-Encoding": "chunked", "Cache-Control": "no-cache,must-revalidate", "Content-Type": "text/html; charset=UTF-8", "Date": "Mon, 14 Jul 2025 06:47:18 GMT", "Pragma": "no-cache", "Server": "JYBC WEB/2.0"}, "content_type": "text/html; charset=UTF-8", "content_length": 1038, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<title>很抱歉</title>\n<meta charset=\"utf-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<style type=\"text/css\">\n    *{ margin:0;padding:0}\n    body { background: #F2F2F2; }\n    .container{padding: 250px 0;text-align: center;}\n</style>\n</head>\n\n<body>\n    <div class=\"container\">\n        <div><img src=\"/images/wuliao/load_page.png\"/></div>\n        <p style=\"font-size:18px\">请重新登录</p>\n        <p style=\"color:#666\">详情请咨询客服电话/QQ：40062668", "is_json": false}, "POST": {"status_code": 200, "headers": {"Connection": "close", "Transfer-Encoding": "chunked", "Cache-Control": "no-cache,must-revalidate", "Content-Type": "text/html; charset=UTF-8", "Date": "Mon, 14 Jul 2025 06:47:18 GMT", "Pragma": "no-cache", "Server": "JYBC WEB/2.0"}, "content_type": "text/html; charset=UTF-8", "content_length": 1038, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<title>很抱歉</title>\n<meta charset=\"utf-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<style type=\"text/css\">\n    *{ margin:0;padding:0}\n    body { background: #F2F2F2; }\n    .container{padding: 250px 0;text-align: center;}\n</style>\n</head>\n\n<body>\n    <div class=\"container\">\n        <div><img src=\"/images/wuliao/load_page.png\"/></div>\n        <p style=\"font-size:18px\">请重新登录</p>\n        <p style=\"color:#666\">详情请咨询客服电话/QQ：40062668", "is_json": false}}}, {"endpoint": "/api/jump/product", "full_url": "https://www.k3.cn/api/jump/product", "methods_tested": ["GET", "POST"], "responses": {"GET": {"status_code": 401, "headers": {"Date": "Mon, 14 Jul 2025 06:47:20 GMT", "Content-Type": "text/html", "Content-Length": "457", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175"}, "content_type": "text/html", "content_length": 457, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head><meta charset=\"UTF-8\"><title>401 Authorization Required</title></head>\r\n<body>\r\n<center><h1>401 Authorization Required</h1></center>\r\n<table>\r\n<tr>\r\n<td>URL:</td>\r\n<td>https://www.k3.cn/p/.html?show=&amp;type=&amp;source_id=0&amp;source_type=</td>\r\n</tr>\r\n<tr>\r\n<td>æ¶é´:</td>\r\n<td>2025/07/14 14:47:20</td>\r\n</tr>\r\n</table>\r\n<hr/>Powered by JYBC WEB/2.0.175<hr><center>å®¢æçµè¯/QQ 4006266818</center>\r\n</body>\r\n</html>\r\n", "is_json": false}, "POST": {"status_code": 401, "headers": {"Date": "Mon, 14 Jul 2025 06:47:20 GMT", "Content-Type": "text/html", "Content-Length": "457", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175"}, "content_type": "text/html", "content_length": 457, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head><meta charset=\"UTF-8\"><title>401 Authorization Required</title></head>\r\n<body>\r\n<center><h1>401 Authorization Required</h1></center>\r\n<table>\r\n<tr>\r\n<td>URL:</td>\r\n<td>https://www.k3.cn/p/.html?show=&amp;type=&amp;source_id=0&amp;source_type=</td>\r\n</tr>\r\n<tr>\r\n<td>æ¶é´:</td>\r\n<td>2025/07/14 14:47:20</td>\r\n</tr>\r\n</table>\r\n<hr/>Powered by JYBC WEB/2.0.175<hr><center>å®¢æçµè¯/QQ 4006266818</center>\r\n</body>\r\n</html>\r\n", "is_json": false}}}, {"endpoint": "/search/api", "full_url": "https://www.k3.cn/search/api", "methods_tested": ["GET", "POST"], "responses": {"GET": {"status_code": 404, "headers": {"Date": "Mon, 14 Jul 2025 06:47:21 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=UTF-8", "content_length": 855, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <title>错误 404</title>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"refresh\" content=\"3;url=https://www.k3.cn\" />\r\n    <style type=\"text/css\">\r\n    body { background: #414141; }\r\n    #content{ background:#414141;padding:100px;margin:0 auto;margin-top:160px;-moz-border-radius:3px;border-radius:3px;text-align:center}\r\n    h3{ color:#cf6;font-size:20px;text-align:center;font-family:", "is_json": false}, "POST": {"status_code": 404, "headers": {"Date": "Mon, 14 Jul 2025 06:47:21 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=UTF-8", "content_length": 855, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <title>错误 404</title>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"refresh\" content=\"3;url=https://www.k3.cn\" />\r\n    <style type=\"text/css\">\r\n    body { background: #414141; }\r\n    #content{ background:#414141;padding:100px;margin:0 auto;margin-top:160px;-moz-border-radius:3px;border-radius:3px;text-align:center}\r\n    h3{ color:#cf6;font-size:20px;text-align:center;font-family:", "is_json": false}}}, {"endpoint": "/api/search", "full_url": "https://www.k3.cn/api/search", "methods_tested": ["GET", "POST"], "responses": {"GET": {"status_code": 404, "headers": {"Date": "Mon, 14 Jul 2025 06:47:22 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=UTF-8", "content_length": 855, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <title>错误 404</title>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"refresh\" content=\"3;url=https://www.k3.cn\" />\r\n    <style type=\"text/css\">\r\n    body { background: #414141; }\r\n    #content{ background:#414141;padding:100px;margin:0 auto;margin-top:160px;-moz-border-radius:3px;border-radius:3px;text-align:center}\r\n    h3{ color:#cf6;font-size:20px;text-align:center;font-family:", "is_json": false}, "POST": {"status_code": 404, "headers": {"Date": "Mon, 14 Jul 2025 06:47:22 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=UTF-8", "content_length": 855, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <title>错误 404</title>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"refresh\" content=\"3;url=https://www.k3.cn\" />\r\n    <style type=\"text/css\">\r\n    body { background: #414141; }\r\n    #content{ background:#414141;padding:100px;margin:0 auto;margin-top:160px;-moz-border-radius:3px;border-radius:3px;text-align:center}\r\n    h3{ color:#cf6;font-size:20px;text-align:center;font-family:", "is_json": false}}}, {"endpoint": "/api/product/list", "full_url": "https://www.k3.cn/api/product/list", "methods_tested": ["GET", "POST"], "responses": {"GET": {"status_code": 404, "headers": {"Date": "Mon, 14 Jul 2025 06:47:23 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=UTF-8", "content_length": 855, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <title>错误 404</title>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"refresh\" content=\"3;url=https://www.k3.cn\" />\r\n    <style type=\"text/css\">\r\n    body { background: #414141; }\r\n    #content{ background:#414141;padding:100px;margin:0 auto;margin-top:160px;-moz-border-radius:3px;border-radius:3px;text-align:center}\r\n    h3{ color:#cf6;font-size:20px;text-align:center;font-family:", "is_json": false}, "POST": {"status_code": 404, "headers": {"Date": "Mon, 14 Jul 2025 06:47:23 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=UTF-8", "content_length": 855, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <title>错误 404</title>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"refresh\" content=\"3;url=https://www.k3.cn\" />\r\n    <style type=\"text/css\">\r\n    body { background: #414141; }\r\n    #content{ background:#414141;padding:100px;margin:0 auto;margin-top:160px;-moz-border-radius:3px;border-radius:3px;text-align:center}\r\n    h3{ color:#cf6;font-size:20px;text-align:center;font-family:", "is_json": false}}}, {"endpoint": "/api/data/search", "full_url": "https://www.k3.cn/api/data/search", "methods_tested": ["GET", "POST"], "responses": {"GET": {"status_code": 404, "headers": {"Date": "Mon, 14 Jul 2025 06:47:24 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=UTF-8", "content_length": 855, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <title>错误 404</title>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"refresh\" content=\"3;url=https://www.k3.cn\" />\r\n    <style type=\"text/css\">\r\n    body { background: #414141; }\r\n    #content{ background:#414141;padding:100px;margin:0 auto;margin-top:160px;-moz-border-radius:3px;border-radius:3px;text-align:center}\r\n    h3{ color:#cf6;font-size:20px;text-align:center;font-family:", "is_json": false}, "POST": {"status_code": 404, "headers": {"Date": "Mon, 14 Jul 2025 06:47:24 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "JYBC WEB/2.0.175", "Content-Encoding": "gzip"}, "content_type": "text/html; charset=UTF-8", "content_length": 855, "content_preview": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <title>错误 404</title>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"refresh\" content=\"3;url=https://www.k3.cn\" />\r\n    <style type=\"text/css\">\r\n    body { background: #414141; }\r\n    #content{ background:#414141;padding:100px;margin:0 auto;margin-top:160px;-moz-border-radius:3px;border-radius:3px;text-align:center}\r\n    h3{ color:#cf6;font-size:20px;text-align:center;font-family:", "is_json": false}}}], "search_api_attempts": [{"path": "/search/api/web", "method": "GET", "params": {"keyword": "运动鞋", "q": "运动鞋", "search": "运动鞋", "type": "all", "page": 1, "limit": 10, "category": "all"}, "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 855}, {"path": "/api/search/web", "method": "GET", "params": {"keyword": "运动鞋", "q": "运动鞋", "search": "运动鞋", "type": "all", "page": 1, "limit": 10, "category": "all"}, "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 855}, {"path": "/api/search/product", "method": "GET", "params": {"keyword": "运动鞋", "q": "运动鞋", "search": "运动鞋", "type": "all", "page": 1, "limit": 10, "category": "all"}, "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 855}, {"path": "/search/product/api", "method": "GET", "params": {"keyword": "运动鞋", "q": "运动鞋", "search": "运动鞋", "type": "all", "page": 1, "limit": 10, "category": "all"}, "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 855}, {"path": "/api/v1/search", "method": "GET", "params": {"keyword": "运动鞋", "q": "运动鞋", "search": "运动鞋", "type": "all", "page": 1, "limit": 10, "category": "all"}, "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 855}, {"path": "/api/search", "method": "GET", "params": {"keyword": "运动鞋", "q": "运动鞋", "search": "运动鞋", "type": "all", "page": 1, "limit": 10, "category": "all"}, "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 855}, {"path": "/search/ajax", "method": "GET", "params": {"keyword": "运动鞋", "q": "运动鞋", "search": "运动鞋", "type": "all", "page": 1, "limit": 10, "category": "all"}, "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 855}], "network_analysis": null}